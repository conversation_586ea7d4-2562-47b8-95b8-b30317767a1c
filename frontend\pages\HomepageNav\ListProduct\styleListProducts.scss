body {
    font-family: sans-serif;
    background-color: #f8f9fa;
}

.top-bar {
    background-color: #fff;
    padding: 15px 0;
    border-bottom: 1px solid #dee2e6;
}

.search-bar {
    display: flex;
    gap: 10px;
}

.product-section {
    padding: 20px 0;
}

.product-card {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 15px;
    display: flex;
    gap: 15px;
    align-items: center;
}

.product-image {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 4px;
}

.product-details {
    flex-grow: 1;
}

.product-actions button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    color: #fff;
}

.btn-cart {
    background-color: #ffc107;
}

.btn-buy {
    background-color: #dc3545;
}

.hotline-section {
    background-color: #ffebee;
    padding: 20px;
}

.footer {
    background-color: #f8f9fa;
    padding: 20px 0;
    text-align: center;
    border-top: 1px solid #dee2e6;
}