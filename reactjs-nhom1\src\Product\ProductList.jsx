import { useEffect, useState } from "react";
import ProductItem from "../components/layouts/productItems/ProductItem";
import { getAllProducts } from "../api/Productsapi";

const ProductList = () => {
  const [products, setProducts] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    getAllProducts()
      .then((response) => {
        setProducts(response.products.slice(0, 9)); // giả sử backend trả về object { products: [...] }
      })
      .catch((err) => {
        setError('<PERSON><PERSON>y sản phẩm thất bại');
        console.error(err);
      });
  }, []);

  if (error) return <div>{error}</div>;

  return (
    <div>
      <h1 className="text-3xl font-bold text-center my-6">Sản phẩm của tôi</h1>
      <div className="flex flex-wrap justify-between">
        {products.map((product) => (
          <ProductItem product={product} key={product.id} />
        ))}
      </div>
    </div>
  );
};
export { ProductList };
