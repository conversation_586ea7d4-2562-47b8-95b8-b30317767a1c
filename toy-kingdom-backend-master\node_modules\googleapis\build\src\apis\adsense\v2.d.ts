/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace adsense_v2 {
    export interface Options extends GlobalOptions {
        version: 'v2';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * AdSense Management API
     *
     * The AdSense Management API allows publishers to access their inventory and run earnings and performance reports.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const adsense = google.adsense('v2');
     * ```
     */
    export class Adsense {
        context: APIRequestContext;
        accounts: Resource$Accounts;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Representation of an account.
     */
    export interface Schema$Account {
        /**
         * Output only. Creation time of the account.
         */
        createTime?: string | null;
        /**
         * Output only. Display name of this account.
         */
        displayName?: string | null;
        /**
         * Output only. Resource name of the account. Format: accounts/pub-[0-9]+
         */
        name?: string | null;
        /**
         * Output only. Outstanding tasks that need to be completed as part of the sign-up process for a new account. e.g. "billing-profile-creation", "phone-pin-verification".
         */
        pendingTasks?: string[] | null;
        /**
         * Output only. Whether this account is premium.
         */
        premium?: boolean | null;
        /**
         * Output only. State of the account.
         */
        state?: string | null;
        /**
         * The account time zone, as used by reporting. For more information, see [changing the time zone of your reports](https://support.google.com/adsense/answer/9830725).
         */
        timeZone?: Schema$TimeZone;
    }
    /**
     * Representation of an ad blocking recovery tag. See https://support.google.com/adsense/answer/********.
     */
    export interface Schema$AdBlockingRecoveryTag {
        /**
         * Error protection code that can be used in conjunction with the tag. It'll display a message to users if an [ad blocking extension blocks their access to your site](https://support.google.com/adsense/answer/********).
         */
        errorProtectionCode?: string | null;
        /**
         * The ad blocking recovery tag. Note that the message generated by the tag can be blocked by an ad blocking extension. If this is not your desired outcome, then you'll need to use it in conjunction with the error protection code.
         */
        tag?: string | null;
    }
    /**
     * Representation of an ad client. An ad client represents a user's subscription with a specific AdSense product.
     */
    export interface Schema$AdClient {
        /**
         * Output only. Resource name of the ad client. Format: accounts/{account\}/adclients/{adclient\}
         */
        name?: string | null;
        /**
         * Output only. Reporting product code of the ad client. For example, "AFC" for AdSense for Content. Corresponds to the `PRODUCT_CODE` dimension, and present only if the ad client supports reporting.
         */
        productCode?: string | null;
        /**
         * Output only. Unique ID of the ad client as used in the `AD_CLIENT_ID` reporting dimension. Present only if the ad client supports reporting.
         */
        reportingDimensionId?: string | null;
        /**
         * Output only. State of the ad client.
         */
        state?: string | null;
    }
    /**
     * Representation of the AdSense code for a given ad client. For more information, see [About the AdSense code](https://support.google.com/adsense/answer/9274634).
     */
    export interface Schema$AdClientAdCode {
        /**
         * Output only. The AdSense code snippet to add to the head of an HTML page.
         */
        adCode?: string | null;
        /**
         * Output only. The AdSense code snippet to add to the body of an AMP page.
         */
        ampBody?: string | null;
        /**
         * Output only. The AdSense code snippet to add to the head of an AMP page.
         */
        ampHead?: string | null;
    }
    /**
     * Representation of an ad unit. An ad unit represents a saved ad unit with a specific set of ad settings that have been customized within an account.
     */
    export interface Schema$AdUnit {
        /**
         * Required. Settings specific to content ads (AFC).
         */
        contentAdsSettings?: Schema$ContentAdsSettings;
        /**
         * Required. Display name of the ad unit, as provided when the ad unit was created.
         */
        displayName?: string | null;
        /**
         * Output only. Resource name of the ad unit. Format: accounts/{account\}/adclients/{adclient\}/adunits/{adunit\}
         */
        name?: string | null;
        /**
         * Output only. Unique ID of the ad unit as used in the `AD_UNIT_ID` reporting dimension.
         */
        reportingDimensionId?: string | null;
        /**
         * Required. State of the ad unit.
         */
        state?: string | null;
    }
    /**
     * Representation of the ad unit code for a given ad unit. For more information, see [About the AdSense code](https://support.google.com/adsense/answer/9274634) and [Where to place the ad code in your HTML](https://support.google.com/adsense/answer/9190028).
     */
    export interface Schema$AdUnitAdCode {
        /**
         * Output only. The code snippet to add to the body of an HTML page.
         */
        adCode?: string | null;
    }
    /**
     * Representation of an alert.
     */
    export interface Schema$Alert {
        /**
         * Output only. The localized alert message. This may contain HTML markup, such as phrase elements or links.
         */
        message?: string | null;
        /**
         * Output only. Resource name of the alert. Format: accounts/{account\}/alerts/{alert\}
         */
        name?: string | null;
        /**
         * Output only. Severity of this alert.
         */
        severity?: string | null;
        /**
         * Output only. Type of alert. This identifies the broad type of this alert, and provides a stable machine-readable identifier that will not be translated. For example, "payment-hold".
         */
        type?: string | null;
    }
    /**
     * Cell representation.
     */
    export interface Schema$Cell {
        /**
         * Value in the cell. The dimension cells contain strings, and the metric cells contain numbers.
         */
        value?: string | null;
    }
    /**
     * Settings specific to content ads (AFC).
     */
    export interface Schema$ContentAdsSettings {
        /**
         * Required. Size of the ad unit. e.g. "728x90", "1x3" (for responsive ad units).
         */
        size?: string | null;
        /**
         * Required. Type of the ad unit.
         */
        type?: string | null;
    }
    /**
     * Representation of a custom channel.
     */
    export interface Schema$CustomChannel {
        /**
         * Whether the custom channel is active and collecting data. See https://support.google.com/adsense/answer/********.
         */
        active?: boolean | null;
        /**
         * Required. Display name of the custom channel.
         */
        displayName?: string | null;
        /**
         * Output only. Resource name of the custom channel. Format: accounts/{account\}/adclients/{adclient\}/customchannels/{customchannel\}
         */
        name?: string | null;
        /**
         * Output only. Unique ID of the custom channel as used in the `CUSTOM_CHANNEL_ID` reporting dimension.
         */
        reportingDimensionId?: string | null;
    }
    /**
     * Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp
     */
    export interface Schema$Date {
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        day?: number | null;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        month?: number | null;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        year?: number | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * The header information of the columns requested in the report.
     */
    export interface Schema$Header {
        /**
         * The [ISO-4217 currency code](https://en.wikipedia.org/wiki/ISO_4217) of this column. Only present if the header type is METRIC_CURRENCY.
         */
        currencyCode?: string | null;
        /**
         * Required. Name of the header.
         */
        name?: string | null;
        /**
         * Required. Type of the header.
         */
        type?: string | null;
    }
    /**
     * Message that represents an arbitrary HTTP body. It should only be used for payload formats that can't be represented as JSON, such as raw binary or an HTML page. This message can be used both in streaming and non-streaming API methods in the request as well as the response. It can be used as a top-level request field, which is convenient if one wants to extract parameters from either the URL or HTTP template into the request fields and also want access to the raw HTTP body. Example: message GetResourceRequest { // A unique request id. string request_id = 1; // The raw HTTP body is bound to this field. google.api.HttpBody http_body = 2; \} service ResourceService { rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); \} Example with streaming methods: service CaldavService { rpc GetCalendar(stream google.api.HttpBody) returns (stream google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns (stream google.api.HttpBody); \} Use of this type only changes how the request and response bodies are handled, all other features will continue to work unchanged.
     */
    export interface Schema$HttpBody {
        /**
         * The HTTP Content-Type header value specifying the content type of the body.
         */
        contentType?: string | null;
        /**
         * The HTTP request/response body as raw binary.
         */
        data?: string | null;
        /**
         * Application specific response metadata. Must be set in the first response for streaming APIs.
         */
        extensions?: Array<{
            [key: string]: any;
        }> | null;
    }
    /**
     * Response definition for the account list rpc.
     */
    export interface Schema$ListAccountsResponse {
        /**
         * The accounts returned in this list response.
         */
        accounts?: Schema$Account[];
        /**
         * Continuation token used to page through accounts. To retrieve the next page of the results, set the next request's "page_token" value to this.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response definition for the ad client list rpc.
     */
    export interface Schema$ListAdClientsResponse {
        /**
         * The ad clients returned in this list response.
         */
        adClients?: Schema$AdClient[];
        /**
         * Continuation token used to page through ad clients. To retrieve the next page of the results, set the next request's "page_token" value to this.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response definition for the adunit list rpc.
     */
    export interface Schema$ListAdUnitsResponse {
        /**
         * The ad units returned in the list response.
         */
        adUnits?: Schema$AdUnit[];
        /**
         * Continuation token used to page through ad units. To retrieve the next page of the results, set the next request's "page_token" value to this.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response definition for the alerts list rpc.
     */
    export interface Schema$ListAlertsResponse {
        /**
         * The alerts returned in this list response.
         */
        alerts?: Schema$Alert[];
    }
    /**
     * Response definition for the child account list rpc.
     */
    export interface Schema$ListChildAccountsResponse {
        /**
         * The accounts returned in this list response.
         */
        accounts?: Schema$Account[];
        /**
         * Continuation token used to page through accounts. To retrieve the next page of the results, set the next request's "page_token" value to this.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response definition for the custom channel list rpc.
     */
    export interface Schema$ListCustomChannelsResponse {
        /**
         * The custom channels returned in this list response.
         */
        customChannels?: Schema$CustomChannel[];
        /**
         * Continuation token used to page through alerts. To retrieve the next page of the results, set the next request's "page_token" value to this.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response definition for the ad units linked to a custom channel list rpc.
     */
    export interface Schema$ListLinkedAdUnitsResponse {
        /**
         * The ad units returned in the list response.
         */
        adUnits?: Schema$AdUnit[];
        /**
         * Continuation token used to page through ad units. To retrieve the next page of the results, set the next request's "page_token" value to this.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response definition for the custom channels linked to an adunit list rpc.
     */
    export interface Schema$ListLinkedCustomChannelsResponse {
        /**
         * The custom channels returned in this list response.
         */
        customChannels?: Schema$CustomChannel[];
        /**
         * Continuation token used to page through alerts. To retrieve the next page of the results, set the next request's "page_token" value to this.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response definition for the payments list rpc.
     */
    export interface Schema$ListPaymentsResponse {
        /**
         * The payments returned in this list response.
         */
        payments?: Schema$Payment[];
    }
    /**
     * Response definition for the policy issues list rpc. Policy issues are reported only if the publisher has at least one AFC ad client in READY or GETTING_READY state. If the publisher has no such AFC ad client, the response will be an empty list.
     */
    export interface Schema$ListPolicyIssuesResponse {
        /**
         * Continuation token used to page through policy issues. To retrieve the next page of the results, set the next request's "page_token" value to this.
         */
        nextPageToken?: string | null;
        /**
         * The policy issues returned in the list response.
         */
        policyIssues?: Schema$PolicyIssue[];
    }
    /**
     * Response definition for the saved reports list rpc.
     */
    export interface Schema$ListSavedReportsResponse {
        /**
         * Continuation token used to page through reports. To retrieve the next page of the results, set the next request's "page_token" value to this.
         */
        nextPageToken?: string | null;
        /**
         * The reports returned in this list response.
         */
        savedReports?: Schema$SavedReport[];
    }
    /**
     * Response definition for the sites list rpc.
     */
    export interface Schema$ListSitesResponse {
        /**
         * Continuation token used to page through sites. To retrieve the next page of the results, set the next request's "page_token" value to this.
         */
        nextPageToken?: string | null;
        /**
         * The sites returned in this list response.
         */
        sites?: Schema$Site[];
    }
    /**
     * Response definition for the url channels list rpc.
     */
    export interface Schema$ListUrlChannelsResponse {
        /**
         * Continuation token used to page through url channels. To retrieve the next page of the results, set the next request's "page_token" value to this.
         */
        nextPageToken?: string | null;
        /**
         * The url channels returned in this list response.
         */
        urlChannels?: Schema$UrlChannel[];
    }
    /**
     * Representation of an unpaid or paid payment. See [Payment timelines for AdSense](https://support.google.com/adsense/answer/7164703) for more information about payments and the [YouTube homepage and payments account](https://support.google.com/adsense/answer/********) article for information about dedicated payments accounts for YouTube.
     */
    export interface Schema$Payment {
        /**
         * Output only. The amount of unpaid or paid earnings, as a formatted string, including the currency. E.g. "¥1,235 JPY", "$1,234.57", "£87.65".
         */
        amount?: string | null;
        /**
         * Output only. For paid earnings, the date that the payment was credited. For unpaid earnings, this field is empty. Payment dates are always returned in the billing timezone (America/Los_Angeles).
         */
        date?: Schema$Date;
        /**
         * Output only. Resource name of the payment. Format: - accounts/{account\}/payments/unpaid for unpaid (current) AdSense earnings. - accounts/{account\}/payments/youtube-unpaid for unpaid (current) YouTube earnings. - accounts/{account\}/payments/yyyy-MM-dd for paid AdSense earnings. - accounts/{account\}/payments/youtube-yyyy-MM-dd for paid YouTube earnings.
         */
        name?: string | null;
    }
    /**
     * Representation of a policy issue for a single entity (site, site-section, or page). All issues for a single entity are represented by a single PolicyIssue resource, though that PolicyIssue can have multiple causes (or "topics") that can change over time. Policy issues are removed if there are no issues detected recently or if there's a recent successful appeal for the entity.
     */
    export interface Schema$PolicyIssue {
        /**
         * Required. The most severe action taken on the entity over the past seven days.
         */
        action?: string | null;
        /**
         * Optional. List of ad clients associated with the policy issue (either as the primary ad client or an associated host/secondary ad client). In the latter case, this will be an ad client that is not owned by the current account.
         */
        adClients?: string[] | null;
        /**
         * Required. Total number of ad requests affected by the policy violations over the past seven days.
         */
        adRequestCount?: string | null;
        /**
         * Required. Type of the entity indicating if the entity is a site, site-section, or page.
         */
        entityType?: string | null;
        /**
         * Required. The date (in the America/Los_Angeles timezone) when policy violations were first detected on the entity.
         */
        firstDetectedDate?: Schema$Date;
        /**
         * Required. The date (in the America/Los_Angeles timezone) when policy violations were last detected on the entity.
         */
        lastDetectedDate?: Schema$Date;
        /**
         * Required. Resource name of the entity with policy issues. Format: accounts/{account\}/policyIssues/{policy_issue\}
         */
        name?: string | null;
        /**
         * Required. Unordered list. The policy topics that this entity was found to violate over the past seven days.
         */
        policyTopics?: Schema$PolicyTopic[];
        /**
         * Required. Hostname/domain of the entity (for example "foo.com" or "www.foo.com"). This _should_ be a bare domain/host name without any protocol. This will be present for all policy issues.
         */
        site?: string | null;
        /**
         * Optional. Prefix of the site-section having policy issues (For example "foo.com/bar-section"). This will be present if the `entity_type` is `SITE_SECTION` and will be absent for other entity types.
         */
        siteSection?: string | null;
        /**
         * Optional. URI of the page having policy violations (for example "foo.com/bar" or "www.foo.com/bar"). This will be present if the `entity_type` is `PAGE` and will be absent for other entity types.
         */
        uri?: string | null;
        /**
         * Optional. The date (in the America/Los_Angeles timezone) when the entity will have ad serving demand restricted or ad serving disabled. This is present only for issues with a `WARNED` enforcement action. See https://support.google.com/adsense/answer/11066888.
         */
        warningEscalationDate?: Schema$Date;
    }
    /**
     * Information about a particular policy topic. A policy topic represents a single class of policy issue that can impact ad serving for your site. For example, sexual content or having ads that obscure your content. A single policy issue can have multiple policy topics for a single entity.
     */
    export interface Schema$PolicyTopic {
        /**
         * Required. Indicates if this is a policy violation or not. When the value is true, issues that are instances of this topic must be addressed to remain in compliance with the partner's agreements with Google. A false value indicates that it's not mandatory to fix the issues but advertising demand might be restricted.
         */
        mustFix?: boolean | null;
        /**
         * Required. The policy topic. For example, "sexual-content" or "ads-obscuring-content"."
         */
        topic?: string | null;
    }
    /**
     * Result of a generated report.
     */
    export interface Schema$ReportResult {
        /**
         * The averages of the report. This is the same length as any other row in the report; cells corresponding to dimension columns are empty.
         */
        averages?: Schema$Row;
        /**
         * Required. End date of the range (inclusive).
         */
        endDate?: Schema$Date;
        /**
         * The header information; one for each dimension in the request, followed by one for each metric in the request.
         */
        headers?: Schema$Header[];
        /**
         * The output rows of the report. Each row is a list of cells; one for each dimension in the request, followed by one for each metric in the request.
         */
        rows?: Schema$Row[];
        /**
         * Required. Start date of the range (inclusive).
         */
        startDate?: Schema$Date;
        /**
         * The total number of rows matched by the report request.
         */
        totalMatchedRows?: string | null;
        /**
         * The totals of the report. This is the same length as any other row in the report; cells corresponding to dimension columns are empty.
         */
        totals?: Schema$Row;
        /**
         * Any warnings associated with generation of the report. These warnings are always returned in English.
         */
        warnings?: string[] | null;
    }
    /**
     * Row representation.
     */
    export interface Schema$Row {
        /**
         * Cells in the row.
         */
        cells?: Schema$Cell[];
    }
    /**
     * Representation of a saved report.
     */
    export interface Schema$SavedReport {
        /**
         * Output only. Resource name of the report. Format: accounts/{account\}/reports/{report\}
         */
        name?: string | null;
        /**
         * Report title as specified by publisher.
         */
        title?: string | null;
    }
    /**
     * Representation of a Site.
     */
    export interface Schema$Site {
        /**
         * Whether auto ads is turned on for the site.
         */
        autoAdsEnabled?: boolean | null;
        /**
         * Domain (or subdomain) of the site, e.g. "example.com" or "www.example.com". This is used in the `OWNED_SITE_DOMAIN_NAME` reporting dimension.
         */
        domain?: string | null;
        /**
         * Output only. Resource name of a site. Format: accounts/{account\}/sites/{site\}
         */
        name?: string | null;
        /**
         * Output only. Unique ID of the site as used in the `OWNED_SITE_ID` reporting dimension.
         */
        reportingDimensionId?: string | null;
        /**
         * Output only. State of a site.
         */
        state?: string | null;
    }
    /**
     * Represents a time zone from the [IANA Time Zone Database](https://www.iana.org/time-zones).
     */
    export interface Schema$TimeZone {
        /**
         * IANA Time Zone Database time zone, e.g. "America/New_York".
         */
        id?: string | null;
        /**
         * Optional. IANA Time Zone Database version number, e.g. "2019a".
         */
        version?: string | null;
    }
    /**
     * Representation of a URL channel. URL channels allow you to track the performance of particular pages in your site; see [URL channels](https://support.google.com/adsense/answer/2923836) for more information.
     */
    export interface Schema$UrlChannel {
        /**
         * Output only. Resource name of the URL channel. Format: accounts/{account\}/adclients/{adclient\}/urlchannels/{urlchannel\}
         */
        name?: string | null;
        /**
         * Output only. Unique ID of the custom channel as used in the `URL_CHANNEL_ID` reporting dimension.
         */
        reportingDimensionId?: string | null;
        /**
         * URI pattern of the channel. Does not include "http://" or "https://". Example: www.example.com/home
         */
        uriPattern?: string | null;
    }
    export class Resource$Accounts {
        context: APIRequestContext;
        adclients: Resource$Accounts$Adclients;
        alerts: Resource$Accounts$Alerts;
        payments: Resource$Accounts$Payments;
        policyIssues: Resource$Accounts$Policyissues;
        reports: Resource$Accounts$Reports;
        sites: Resource$Accounts$Sites;
        constructor(context: APIRequestContext);
        /**
         * Gets information about the selected AdSense account.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Accounts$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Accounts$Get, options?: MethodOptions): GaxiosPromise<Schema$Account>;
        get(params: Params$Resource$Accounts$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Accounts$Get, options: MethodOptions | BodyResponseCallback<Schema$Account>, callback: BodyResponseCallback<Schema$Account>): void;
        get(params: Params$Resource$Accounts$Get, callback: BodyResponseCallback<Schema$Account>): void;
        get(callback: BodyResponseCallback<Schema$Account>): void;
        /**
         * Gets the ad blocking recovery tag of an account.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getAdBlockingRecoveryTag(params: Params$Resource$Accounts$Getadblockingrecoverytag, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getAdBlockingRecoveryTag(params?: Params$Resource$Accounts$Getadblockingrecoverytag, options?: MethodOptions): GaxiosPromise<Schema$AdBlockingRecoveryTag>;
        getAdBlockingRecoveryTag(params: Params$Resource$Accounts$Getadblockingrecoverytag, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getAdBlockingRecoveryTag(params: Params$Resource$Accounts$Getadblockingrecoverytag, options: MethodOptions | BodyResponseCallback<Schema$AdBlockingRecoveryTag>, callback: BodyResponseCallback<Schema$AdBlockingRecoveryTag>): void;
        getAdBlockingRecoveryTag(params: Params$Resource$Accounts$Getadblockingrecoverytag, callback: BodyResponseCallback<Schema$AdBlockingRecoveryTag>): void;
        getAdBlockingRecoveryTag(callback: BodyResponseCallback<Schema$AdBlockingRecoveryTag>): void;
        /**
         * Lists all accounts available to this user.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Accounts$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Accounts$List, options?: MethodOptions): GaxiosPromise<Schema$ListAccountsResponse>;
        list(params: Params$Resource$Accounts$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Accounts$List, options: MethodOptions | BodyResponseCallback<Schema$ListAccountsResponse>, callback: BodyResponseCallback<Schema$ListAccountsResponse>): void;
        list(params: Params$Resource$Accounts$List, callback: BodyResponseCallback<Schema$ListAccountsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAccountsResponse>): void;
        /**
         * Lists all accounts directly managed by the given AdSense account.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listChildAccounts(params: Params$Resource$Accounts$Listchildaccounts, options: StreamMethodOptions): GaxiosPromise<Readable>;
        listChildAccounts(params?: Params$Resource$Accounts$Listchildaccounts, options?: MethodOptions): GaxiosPromise<Schema$ListChildAccountsResponse>;
        listChildAccounts(params: Params$Resource$Accounts$Listchildaccounts, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listChildAccounts(params: Params$Resource$Accounts$Listchildaccounts, options: MethodOptions | BodyResponseCallback<Schema$ListChildAccountsResponse>, callback: BodyResponseCallback<Schema$ListChildAccountsResponse>): void;
        listChildAccounts(params: Params$Resource$Accounts$Listchildaccounts, callback: BodyResponseCallback<Schema$ListChildAccountsResponse>): void;
        listChildAccounts(callback: BodyResponseCallback<Schema$ListChildAccountsResponse>): void;
    }
    export interface Params$Resource$Accounts$Get extends StandardParameters {
        /**
         * Required. Account to get information about. Format: accounts/{account\}
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Getadblockingrecoverytag extends StandardParameters {
        /**
         * Required. The name of the account to get the tag for. Format: accounts/{account\}
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$List extends StandardParameters {
        /**
         * The maximum number of accounts to include in the response, used for paging. If unspecified, at most 10000 accounts will be returned. The maximum value is 10000; values above 10000 will be coerced to 10000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListAccounts` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAccounts` must match the call that provided the page token.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Accounts$Listchildaccounts extends StandardParameters {
        /**
         * The maximum number of accounts to include in the response, used for paging. If unspecified, at most 10000 accounts will be returned. The maximum value is 10000; values above 10000 will be coerced to 10000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListChildAccounts` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListChildAccounts` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The parent account, which owns the child accounts. Format: accounts/{account\}
         */
        parent?: string;
    }
    export class Resource$Accounts$Adclients {
        context: APIRequestContext;
        adunits: Resource$Accounts$Adclients$Adunits;
        customchannels: Resource$Accounts$Adclients$Customchannels;
        urlchannels: Resource$Accounts$Adclients$Urlchannels;
        constructor(context: APIRequestContext);
        /**
         * Gets the ad client from the given resource name.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Accounts$Adclients$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Accounts$Adclients$Get, options?: MethodOptions): GaxiosPromise<Schema$AdClient>;
        get(params: Params$Resource$Accounts$Adclients$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Accounts$Adclients$Get, options: MethodOptions | BodyResponseCallback<Schema$AdClient>, callback: BodyResponseCallback<Schema$AdClient>): void;
        get(params: Params$Resource$Accounts$Adclients$Get, callback: BodyResponseCallback<Schema$AdClient>): void;
        get(callback: BodyResponseCallback<Schema$AdClient>): void;
        /**
         * Gets the AdSense code for a given ad client. This returns what was previously known as the 'auto ad code'. This is only supported for ad clients with a product_code of AFC. For more information, see [About the AdSense code](https://support.google.com/adsense/answer/9274634).
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getAdcode(params: Params$Resource$Accounts$Adclients$Getadcode, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getAdcode(params?: Params$Resource$Accounts$Adclients$Getadcode, options?: MethodOptions): GaxiosPromise<Schema$AdClientAdCode>;
        getAdcode(params: Params$Resource$Accounts$Adclients$Getadcode, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getAdcode(params: Params$Resource$Accounts$Adclients$Getadcode, options: MethodOptions | BodyResponseCallback<Schema$AdClientAdCode>, callback: BodyResponseCallback<Schema$AdClientAdCode>): void;
        getAdcode(params: Params$Resource$Accounts$Adclients$Getadcode, callback: BodyResponseCallback<Schema$AdClientAdCode>): void;
        getAdcode(callback: BodyResponseCallback<Schema$AdClientAdCode>): void;
        /**
         * Lists all the ad clients available in an account.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Accounts$Adclients$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Accounts$Adclients$List, options?: MethodOptions): GaxiosPromise<Schema$ListAdClientsResponse>;
        list(params: Params$Resource$Accounts$Adclients$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Accounts$Adclients$List, options: MethodOptions | BodyResponseCallback<Schema$ListAdClientsResponse>, callback: BodyResponseCallback<Schema$ListAdClientsResponse>): void;
        list(params: Params$Resource$Accounts$Adclients$List, callback: BodyResponseCallback<Schema$ListAdClientsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAdClientsResponse>): void;
    }
    export interface Params$Resource$Accounts$Adclients$Get extends StandardParameters {
        /**
         * Required. The name of the ad client to retrieve. Format: accounts/{account\}/adclients/{adclient\}
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Adclients$Getadcode extends StandardParameters {
        /**
         * Required. Name of the ad client for which to get the adcode. Format: accounts/{account\}/adclients/{adclient\}
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Adclients$List extends StandardParameters {
        /**
         * The maximum number of ad clients to include in the response, used for paging. If unspecified, at most 10000 ad clients will be returned. The maximum value is 10000; values above 10000 will be coerced to 10000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListAdClients` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAdClients` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The account which owns the collection of ad clients. Format: accounts/{account\}
         */
        parent?: string;
    }
    export class Resource$Accounts$Adclients$Adunits {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates an ad unit. This method can be called only by a restricted set of projects, which are usually owned by [AdSense for Platforms](https://developers.google.com/adsense/platforms/) publishers. Contact your account manager if you need to use this method. Note that ad units can only be created for ad clients with an "AFC" product code. For more info see the [AdClient resource](/adsense/management/reference/rest/v2/accounts.adclients). For now, this method can only be used to create `DISPLAY` ad units. See: https://support.google.com/adsense/answer/9183566
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Accounts$Adclients$Adunits$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Accounts$Adclients$Adunits$Create, options?: MethodOptions): GaxiosPromise<Schema$AdUnit>;
        create(params: Params$Resource$Accounts$Adclients$Adunits$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Accounts$Adclients$Adunits$Create, options: MethodOptions | BodyResponseCallback<Schema$AdUnit>, callback: BodyResponseCallback<Schema$AdUnit>): void;
        create(params: Params$Resource$Accounts$Adclients$Adunits$Create, callback: BodyResponseCallback<Schema$AdUnit>): void;
        create(callback: BodyResponseCallback<Schema$AdUnit>): void;
        /**
         * Gets an ad unit from a specified account and ad client.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Accounts$Adclients$Adunits$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Accounts$Adclients$Adunits$Get, options?: MethodOptions): GaxiosPromise<Schema$AdUnit>;
        get(params: Params$Resource$Accounts$Adclients$Adunits$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Accounts$Adclients$Adunits$Get, options: MethodOptions | BodyResponseCallback<Schema$AdUnit>, callback: BodyResponseCallback<Schema$AdUnit>): void;
        get(params: Params$Resource$Accounts$Adclients$Adunits$Get, callback: BodyResponseCallback<Schema$AdUnit>): void;
        get(callback: BodyResponseCallback<Schema$AdUnit>): void;
        /**
         * Gets the ad unit code for a given ad unit. For more information, see [About the AdSense code](https://support.google.com/adsense/answer/9274634) and [Where to place the ad code in your HTML](https://support.google.com/adsense/answer/9190028).
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getAdcode(params: Params$Resource$Accounts$Adclients$Adunits$Getadcode, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getAdcode(params?: Params$Resource$Accounts$Adclients$Adunits$Getadcode, options?: MethodOptions): GaxiosPromise<Schema$AdUnitAdCode>;
        getAdcode(params: Params$Resource$Accounts$Adclients$Adunits$Getadcode, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getAdcode(params: Params$Resource$Accounts$Adclients$Adunits$Getadcode, options: MethodOptions | BodyResponseCallback<Schema$AdUnitAdCode>, callback: BodyResponseCallback<Schema$AdUnitAdCode>): void;
        getAdcode(params: Params$Resource$Accounts$Adclients$Adunits$Getadcode, callback: BodyResponseCallback<Schema$AdUnitAdCode>): void;
        getAdcode(callback: BodyResponseCallback<Schema$AdUnitAdCode>): void;
        /**
         * Lists all ad units under a specified account and ad client.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Accounts$Adclients$Adunits$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Accounts$Adclients$Adunits$List, options?: MethodOptions): GaxiosPromise<Schema$ListAdUnitsResponse>;
        list(params: Params$Resource$Accounts$Adclients$Adunits$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Accounts$Adclients$Adunits$List, options: MethodOptions | BodyResponseCallback<Schema$ListAdUnitsResponse>, callback: BodyResponseCallback<Schema$ListAdUnitsResponse>): void;
        list(params: Params$Resource$Accounts$Adclients$Adunits$List, callback: BodyResponseCallback<Schema$ListAdUnitsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAdUnitsResponse>): void;
        /**
         * Lists all the custom channels available for an ad unit.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listLinkedCustomChannels(params: Params$Resource$Accounts$Adclients$Adunits$Listlinkedcustomchannels, options: StreamMethodOptions): GaxiosPromise<Readable>;
        listLinkedCustomChannels(params?: Params$Resource$Accounts$Adclients$Adunits$Listlinkedcustomchannels, options?: MethodOptions): GaxiosPromise<Schema$ListLinkedCustomChannelsResponse>;
        listLinkedCustomChannels(params: Params$Resource$Accounts$Adclients$Adunits$Listlinkedcustomchannels, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listLinkedCustomChannels(params: Params$Resource$Accounts$Adclients$Adunits$Listlinkedcustomchannels, options: MethodOptions | BodyResponseCallback<Schema$ListLinkedCustomChannelsResponse>, callback: BodyResponseCallback<Schema$ListLinkedCustomChannelsResponse>): void;
        listLinkedCustomChannels(params: Params$Resource$Accounts$Adclients$Adunits$Listlinkedcustomchannels, callback: BodyResponseCallback<Schema$ListLinkedCustomChannelsResponse>): void;
        listLinkedCustomChannels(callback: BodyResponseCallback<Schema$ListLinkedCustomChannelsResponse>): void;
        /**
         * Updates an ad unit. This method can be called only by a restricted set of projects, which are usually owned by [AdSense for Platforms](https://developers.google.com/adsense/platforms/) publishers. Contact your account manager if you need to use this method. For now, this method can only be used to update `DISPLAY` ad units. See: https://support.google.com/adsense/answer/9183566
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Accounts$Adclients$Adunits$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Accounts$Adclients$Adunits$Patch, options?: MethodOptions): GaxiosPromise<Schema$AdUnit>;
        patch(params: Params$Resource$Accounts$Adclients$Adunits$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Accounts$Adclients$Adunits$Patch, options: MethodOptions | BodyResponseCallback<Schema$AdUnit>, callback: BodyResponseCallback<Schema$AdUnit>): void;
        patch(params: Params$Resource$Accounts$Adclients$Adunits$Patch, callback: BodyResponseCallback<Schema$AdUnit>): void;
        patch(callback: BodyResponseCallback<Schema$AdUnit>): void;
    }
    export interface Params$Resource$Accounts$Adclients$Adunits$Create extends StandardParameters {
        /**
         * Required. Ad client to create an ad unit under. Format: accounts/{account\}/adclients/{adclient\}
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AdUnit;
    }
    export interface Params$Resource$Accounts$Adclients$Adunits$Get extends StandardParameters {
        /**
         * Required. AdUnit to get information about. Format: accounts/{account\}/adclients/{adclient\}/adunits/{adunit\}
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Adclients$Adunits$Getadcode extends StandardParameters {
        /**
         * Required. Name of the adunit for which to get the adcode. Format: accounts/{account\}/adclients/{adclient\}/adunits/{adunit\}
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Adclients$Adunits$List extends StandardParameters {
        /**
         * The maximum number of ad units to include in the response, used for paging. If unspecified, at most 10000 ad units will be returned. The maximum value is 10000; values above 10000 will be coerced to 10000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListAdUnits` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAdUnits` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The ad client which owns the collection of ad units. Format: accounts/{account\}/adclients/{adclient\}
         */
        parent?: string;
    }
    export interface Params$Resource$Accounts$Adclients$Adunits$Listlinkedcustomchannels extends StandardParameters {
        /**
         * The maximum number of custom channels to include in the response, used for paging. If unspecified, at most 10000 custom channels will be returned. The maximum value is 10000; values above 10000 will be coerced to 10000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListLinkedCustomChannels` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListLinkedCustomChannels` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The ad unit which owns the collection of custom channels. Format: accounts/{account\}/adclients/{adclient\}/adunits/{adunit\}
         */
        parent?: string;
    }
    export interface Params$Resource$Accounts$Adclients$Adunits$Patch extends StandardParameters {
        /**
         * Output only. Resource name of the ad unit. Format: accounts/{account\}/adclients/{adclient\}/adunits/{adunit\}
         */
        name?: string;
        /**
         * The list of fields to update. If empty, a full update is performed.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AdUnit;
    }
    export class Resource$Accounts$Adclients$Customchannels {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a custom channel. This method can be called only by a restricted set of projects, which are usually owned by [AdSense for Platforms](https://developers.google.com/adsense/platforms/) publishers. Contact your account manager if you need to use this method.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Accounts$Adclients$Customchannels$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Accounts$Adclients$Customchannels$Create, options?: MethodOptions): GaxiosPromise<Schema$CustomChannel>;
        create(params: Params$Resource$Accounts$Adclients$Customchannels$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Accounts$Adclients$Customchannels$Create, options: MethodOptions | BodyResponseCallback<Schema$CustomChannel>, callback: BodyResponseCallback<Schema$CustomChannel>): void;
        create(params: Params$Resource$Accounts$Adclients$Customchannels$Create, callback: BodyResponseCallback<Schema$CustomChannel>): void;
        create(callback: BodyResponseCallback<Schema$CustomChannel>): void;
        /**
         * Deletes a custom channel. This method can be called only by a restricted set of projects, which are usually owned by [AdSense for Platforms](https://developers.google.com/adsense/platforms/) publishers. Contact your account manager if you need to use this method.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Accounts$Adclients$Customchannels$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Accounts$Adclients$Customchannels$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Accounts$Adclients$Customchannels$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Accounts$Adclients$Customchannels$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Accounts$Adclients$Customchannels$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets information about the selected custom channel.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Accounts$Adclients$Customchannels$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Accounts$Adclients$Customchannels$Get, options?: MethodOptions): GaxiosPromise<Schema$CustomChannel>;
        get(params: Params$Resource$Accounts$Adclients$Customchannels$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Accounts$Adclients$Customchannels$Get, options: MethodOptions | BodyResponseCallback<Schema$CustomChannel>, callback: BodyResponseCallback<Schema$CustomChannel>): void;
        get(params: Params$Resource$Accounts$Adclients$Customchannels$Get, callback: BodyResponseCallback<Schema$CustomChannel>): void;
        get(callback: BodyResponseCallback<Schema$CustomChannel>): void;
        /**
         * Lists all the custom channels available in an ad client.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Accounts$Adclients$Customchannels$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Accounts$Adclients$Customchannels$List, options?: MethodOptions): GaxiosPromise<Schema$ListCustomChannelsResponse>;
        list(params: Params$Resource$Accounts$Adclients$Customchannels$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Accounts$Adclients$Customchannels$List, options: MethodOptions | BodyResponseCallback<Schema$ListCustomChannelsResponse>, callback: BodyResponseCallback<Schema$ListCustomChannelsResponse>): void;
        list(params: Params$Resource$Accounts$Adclients$Customchannels$List, callback: BodyResponseCallback<Schema$ListCustomChannelsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListCustomChannelsResponse>): void;
        /**
         * Lists all the ad units available for a custom channel.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listLinkedAdUnits(params: Params$Resource$Accounts$Adclients$Customchannels$Listlinkedadunits, options: StreamMethodOptions): GaxiosPromise<Readable>;
        listLinkedAdUnits(params?: Params$Resource$Accounts$Adclients$Customchannels$Listlinkedadunits, options?: MethodOptions): GaxiosPromise<Schema$ListLinkedAdUnitsResponse>;
        listLinkedAdUnits(params: Params$Resource$Accounts$Adclients$Customchannels$Listlinkedadunits, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listLinkedAdUnits(params: Params$Resource$Accounts$Adclients$Customchannels$Listlinkedadunits, options: MethodOptions | BodyResponseCallback<Schema$ListLinkedAdUnitsResponse>, callback: BodyResponseCallback<Schema$ListLinkedAdUnitsResponse>): void;
        listLinkedAdUnits(params: Params$Resource$Accounts$Adclients$Customchannels$Listlinkedadunits, callback: BodyResponseCallback<Schema$ListLinkedAdUnitsResponse>): void;
        listLinkedAdUnits(callback: BodyResponseCallback<Schema$ListLinkedAdUnitsResponse>): void;
        /**
         * Updates a custom channel. This method can be called only by a restricted set of projects, which are usually owned by [AdSense for Platforms](https://developers.google.com/adsense/platforms/) publishers. Contact your account manager if you need to use this method.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Accounts$Adclients$Customchannels$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Accounts$Adclients$Customchannels$Patch, options?: MethodOptions): GaxiosPromise<Schema$CustomChannel>;
        patch(params: Params$Resource$Accounts$Adclients$Customchannels$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Accounts$Adclients$Customchannels$Patch, options: MethodOptions | BodyResponseCallback<Schema$CustomChannel>, callback: BodyResponseCallback<Schema$CustomChannel>): void;
        patch(params: Params$Resource$Accounts$Adclients$Customchannels$Patch, callback: BodyResponseCallback<Schema$CustomChannel>): void;
        patch(callback: BodyResponseCallback<Schema$CustomChannel>): void;
    }
    export interface Params$Resource$Accounts$Adclients$Customchannels$Create extends StandardParameters {
        /**
         * Required. The ad client to create a custom channel under. Format: accounts/{account\}/adclients/{adclient\}
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CustomChannel;
    }
    export interface Params$Resource$Accounts$Adclients$Customchannels$Delete extends StandardParameters {
        /**
         * Required. Name of the custom channel to delete. Format: accounts/{account\}/adclients/{adclient\}/customchannels/{customchannel\}
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Adclients$Customchannels$Get extends StandardParameters {
        /**
         * Required. Name of the custom channel. Format: accounts/{account\}/adclients/{adclient\}/customchannels/{customchannel\}
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Adclients$Customchannels$List extends StandardParameters {
        /**
         * The maximum number of custom channels to include in the response, used for paging. If unspecified, at most 10000 custom channels will be returned. The maximum value is 10000; values above 10000 will be coerced to 10000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListCustomChannels` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListCustomChannels` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The ad client which owns the collection of custom channels. Format: accounts/{account\}/adclients/{adclient\}
         */
        parent?: string;
    }
    export interface Params$Resource$Accounts$Adclients$Customchannels$Listlinkedadunits extends StandardParameters {
        /**
         * The maximum number of ad units to include in the response, used for paging. If unspecified, at most 10000 ad units will be returned. The maximum value is 10000; values above 10000 will be coerced to 10000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListLinkedAdUnits` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListLinkedAdUnits` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The custom channel which owns the collection of ad units. Format: accounts/{account\}/adclients/{adclient\}/customchannels/{customchannel\}
         */
        parent?: string;
    }
    export interface Params$Resource$Accounts$Adclients$Customchannels$Patch extends StandardParameters {
        /**
         * Output only. Resource name of the custom channel. Format: accounts/{account\}/adclients/{adclient\}/customchannels/{customchannel\}
         */
        name?: string;
        /**
         * The list of fields to update. If empty, a full update is performed.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CustomChannel;
    }
    export class Resource$Accounts$Adclients$Urlchannels {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets information about the selected url channel.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Accounts$Adclients$Urlchannels$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Accounts$Adclients$Urlchannels$Get, options?: MethodOptions): GaxiosPromise<Schema$UrlChannel>;
        get(params: Params$Resource$Accounts$Adclients$Urlchannels$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Accounts$Adclients$Urlchannels$Get, options: MethodOptions | BodyResponseCallback<Schema$UrlChannel>, callback: BodyResponseCallback<Schema$UrlChannel>): void;
        get(params: Params$Resource$Accounts$Adclients$Urlchannels$Get, callback: BodyResponseCallback<Schema$UrlChannel>): void;
        get(callback: BodyResponseCallback<Schema$UrlChannel>): void;
        /**
         * Lists active url channels.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Accounts$Adclients$Urlchannels$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Accounts$Adclients$Urlchannels$List, options?: MethodOptions): GaxiosPromise<Schema$ListUrlChannelsResponse>;
        list(params: Params$Resource$Accounts$Adclients$Urlchannels$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Accounts$Adclients$Urlchannels$List, options: MethodOptions | BodyResponseCallback<Schema$ListUrlChannelsResponse>, callback: BodyResponseCallback<Schema$ListUrlChannelsResponse>): void;
        list(params: Params$Resource$Accounts$Adclients$Urlchannels$List, callback: BodyResponseCallback<Schema$ListUrlChannelsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListUrlChannelsResponse>): void;
    }
    export interface Params$Resource$Accounts$Adclients$Urlchannels$Get extends StandardParameters {
        /**
         * Required. The name of the url channel to retrieve. Format: accounts/{account\}/adclients/{adclient\}/urlchannels/{urlchannel\}
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Adclients$Urlchannels$List extends StandardParameters {
        /**
         * The maximum number of url channels to include in the response, used for paging. If unspecified, at most 10000 url channels will be returned. The maximum value is 10000; values above 10000 will be coerced to 10000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListUrlChannels` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListUrlChannels` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The ad client which owns the collection of url channels. Format: accounts/{account\}/adclients/{adclient\}
         */
        parent?: string;
    }
    export class Resource$Accounts$Alerts {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Lists all the alerts available in an account.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Accounts$Alerts$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Accounts$Alerts$List, options?: MethodOptions): GaxiosPromise<Schema$ListAlertsResponse>;
        list(params: Params$Resource$Accounts$Alerts$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Accounts$Alerts$List, options: MethodOptions | BodyResponseCallback<Schema$ListAlertsResponse>, callback: BodyResponseCallback<Schema$ListAlertsResponse>): void;
        list(params: Params$Resource$Accounts$Alerts$List, callback: BodyResponseCallback<Schema$ListAlertsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAlertsResponse>): void;
    }
    export interface Params$Resource$Accounts$Alerts$List extends StandardParameters {
        /**
         * The language to use for translating alert messages. If unspecified, this defaults to the user's display language. If the given language is not supported, alerts will be returned in English. The language is specified as an [IETF BCP-47 language code](https://en.wikipedia.org/wiki/IETF_language_tag).
         */
        languageCode?: string;
        /**
         * Required. The account which owns the collection of alerts. Format: accounts/{account\}
         */
        parent?: string;
    }
    export class Resource$Accounts$Payments {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Lists all the payments available for an account.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Accounts$Payments$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Accounts$Payments$List, options?: MethodOptions): GaxiosPromise<Schema$ListPaymentsResponse>;
        list(params: Params$Resource$Accounts$Payments$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Accounts$Payments$List, options: MethodOptions | BodyResponseCallback<Schema$ListPaymentsResponse>, callback: BodyResponseCallback<Schema$ListPaymentsResponse>): void;
        list(params: Params$Resource$Accounts$Payments$List, callback: BodyResponseCallback<Schema$ListPaymentsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListPaymentsResponse>): void;
    }
    export interface Params$Resource$Accounts$Payments$List extends StandardParameters {
        /**
         * Required. The account which owns the collection of payments. Format: accounts/{account\}
         */
        parent?: string;
    }
    export class Resource$Accounts$Policyissues {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets information about the selected policy issue.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Accounts$Policyissues$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Accounts$Policyissues$Get, options?: MethodOptions): GaxiosPromise<Schema$PolicyIssue>;
        get(params: Params$Resource$Accounts$Policyissues$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Accounts$Policyissues$Get, options: MethodOptions | BodyResponseCallback<Schema$PolicyIssue>, callback: BodyResponseCallback<Schema$PolicyIssue>): void;
        get(params: Params$Resource$Accounts$Policyissues$Get, callback: BodyResponseCallback<Schema$PolicyIssue>): void;
        get(callback: BodyResponseCallback<Schema$PolicyIssue>): void;
        /**
         * Lists all the policy issues where the specified account is involved, both directly and through any AFP child accounts.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Accounts$Policyissues$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Accounts$Policyissues$List, options?: MethodOptions): GaxiosPromise<Schema$ListPolicyIssuesResponse>;
        list(params: Params$Resource$Accounts$Policyissues$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Accounts$Policyissues$List, options: MethodOptions | BodyResponseCallback<Schema$ListPolicyIssuesResponse>, callback: BodyResponseCallback<Schema$ListPolicyIssuesResponse>): void;
        list(params: Params$Resource$Accounts$Policyissues$List, callback: BodyResponseCallback<Schema$ListPolicyIssuesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListPolicyIssuesResponse>): void;
    }
    export interface Params$Resource$Accounts$Policyissues$Get extends StandardParameters {
        /**
         * Required. Name of the policy issue. Format: accounts/{account\}/policyIssues/{policy_issue\}
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Policyissues$List extends StandardParameters {
        /**
         * The maximum number of policy issues to include in the response, used for paging. If unspecified, at most 10000 policy issues will be returned. The maximum value is 10000; values above 10000 will be coerced to 10000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListPolicyIssues` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListPolicyIssues` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The account for which policy issues are being retrieved. Format: accounts/{account\}
         */
        parent?: string;
    }
    export class Resource$Accounts$Reports {
        context: APIRequestContext;
        saved: Resource$Accounts$Reports$Saved;
        constructor(context: APIRequestContext);
        /**
         * Generates an ad hoc report.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        generate(params: Params$Resource$Accounts$Reports$Generate, options: StreamMethodOptions): GaxiosPromise<Readable>;
        generate(params?: Params$Resource$Accounts$Reports$Generate, options?: MethodOptions): GaxiosPromise<Schema$ReportResult>;
        generate(params: Params$Resource$Accounts$Reports$Generate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        generate(params: Params$Resource$Accounts$Reports$Generate, options: MethodOptions | BodyResponseCallback<Schema$ReportResult>, callback: BodyResponseCallback<Schema$ReportResult>): void;
        generate(params: Params$Resource$Accounts$Reports$Generate, callback: BodyResponseCallback<Schema$ReportResult>): void;
        generate(callback: BodyResponseCallback<Schema$ReportResult>): void;
        /**
         * Generates a csv formatted ad hoc report.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        generateCsv(params: Params$Resource$Accounts$Reports$Generatecsv, options: StreamMethodOptions): GaxiosPromise<Readable>;
        generateCsv(params?: Params$Resource$Accounts$Reports$Generatecsv, options?: MethodOptions): GaxiosPromise<Schema$HttpBody>;
        generateCsv(params: Params$Resource$Accounts$Reports$Generatecsv, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        generateCsv(params: Params$Resource$Accounts$Reports$Generatecsv, options: MethodOptions | BodyResponseCallback<Schema$HttpBody>, callback: BodyResponseCallback<Schema$HttpBody>): void;
        generateCsv(params: Params$Resource$Accounts$Reports$Generatecsv, callback: BodyResponseCallback<Schema$HttpBody>): void;
        generateCsv(callback: BodyResponseCallback<Schema$HttpBody>): void;
        /**
         * Gets the saved report from the given resource name.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getSaved(params: Params$Resource$Accounts$Reports$Getsaved, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getSaved(params?: Params$Resource$Accounts$Reports$Getsaved, options?: MethodOptions): GaxiosPromise<Schema$SavedReport>;
        getSaved(params: Params$Resource$Accounts$Reports$Getsaved, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getSaved(params: Params$Resource$Accounts$Reports$Getsaved, options: MethodOptions | BodyResponseCallback<Schema$SavedReport>, callback: BodyResponseCallback<Schema$SavedReport>): void;
        getSaved(params: Params$Resource$Accounts$Reports$Getsaved, callback: BodyResponseCallback<Schema$SavedReport>): void;
        getSaved(callback: BodyResponseCallback<Schema$SavedReport>): void;
    }
    export interface Params$Resource$Accounts$Reports$Generate extends StandardParameters {
        /**
         * Required. The account which owns the collection of reports. Format: accounts/{account\}
         */
        account?: string;
        /**
         * The [ISO-4217 currency code](https://en.wikipedia.org/wiki/ISO_4217) to use when reporting on monetary metrics. Defaults to the account's currency if not set.
         */
        currencyCode?: string;
        /**
         * Date range of the report, if unset the range will be considered CUSTOM.
         */
        dateRange?: string;
        /**
         * Dimensions to base the report on.
         */
        dimensions?: string[];
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        'endDate.day'?: number;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        'endDate.month'?: number;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        'endDate.year'?: number;
        /**
         * A list of [filters](/adsense/management/reporting/filtering) to apply to the report. All provided filters must match in order for the data to be included in the report.
         */
        filters?: string[];
        /**
         * The language to use for translating report output. If unspecified, this defaults to English ("en"). If the given language is not supported, report output will be returned in English. The language is specified as an [IETF BCP-47 language code](https://en.wikipedia.org/wiki/IETF_language_tag).
         */
        languageCode?: string;
        /**
         * The maximum number of rows of report data to return. Reports producing more rows than the requested limit will be truncated. If unset, this defaults to 100,000 rows for `Reports.GenerateReport` and 1,000,000 rows for `Reports.GenerateCsvReport`, which are also the maximum values permitted here. Report truncation can be identified (for `Reports.GenerateReport` only) by comparing the number of rows returned to the value returned in `total_matched_rows`.
         */
        limit?: number;
        /**
         * Required. Reporting metrics.
         */
        metrics?: string[];
        /**
         * The name of a dimension or metric to sort the resulting report on, can be prefixed with "+" to sort ascending or "-" to sort descending. If no prefix is specified, the column is sorted ascending.
         */
        orderBy?: string[];
        /**
         * Timezone in which to generate the report. If unspecified, this defaults to the account timezone. For more information, see [changing the time zone of your reports](https://support.google.com/adsense/answer/9830725).
         */
        reportingTimeZone?: string;
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        'startDate.day'?: number;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        'startDate.month'?: number;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        'startDate.year'?: number;
    }
    export interface Params$Resource$Accounts$Reports$Generatecsv extends StandardParameters {
        /**
         * Required. The account which owns the collection of reports. Format: accounts/{account\}
         */
        account?: string;
        /**
         * The [ISO-4217 currency code](https://en.wikipedia.org/wiki/ISO_4217) to use when reporting on monetary metrics. Defaults to the account's currency if not set.
         */
        currencyCode?: string;
        /**
         * Date range of the report, if unset the range will be considered CUSTOM.
         */
        dateRange?: string;
        /**
         * Dimensions to base the report on.
         */
        dimensions?: string[];
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        'endDate.day'?: number;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        'endDate.month'?: number;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        'endDate.year'?: number;
        /**
         * A list of [filters](/adsense/management/reporting/filtering) to apply to the report. All provided filters must match in order for the data to be included in the report.
         */
        filters?: string[];
        /**
         * The language to use for translating report output. If unspecified, this defaults to English ("en"). If the given language is not supported, report output will be returned in English. The language is specified as an [IETF BCP-47 language code](https://en.wikipedia.org/wiki/IETF_language_tag).
         */
        languageCode?: string;
        /**
         * The maximum number of rows of report data to return. Reports producing more rows than the requested limit will be truncated. If unset, this defaults to 100,000 rows for `Reports.GenerateReport` and 1,000,000 rows for `Reports.GenerateCsvReport`, which are also the maximum values permitted here. Report truncation can be identified (for `Reports.GenerateReport` only) by comparing the number of rows returned to the value returned in `total_matched_rows`.
         */
        limit?: number;
        /**
         * Required. Reporting metrics.
         */
        metrics?: string[];
        /**
         * The name of a dimension or metric to sort the resulting report on, can be prefixed with "+" to sort ascending or "-" to sort descending. If no prefix is specified, the column is sorted ascending.
         */
        orderBy?: string[];
        /**
         * Timezone in which to generate the report. If unspecified, this defaults to the account timezone. For more information, see [changing the time zone of your reports](https://support.google.com/adsense/answer/9830725).
         */
        reportingTimeZone?: string;
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        'startDate.day'?: number;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        'startDate.month'?: number;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        'startDate.year'?: number;
    }
    export interface Params$Resource$Accounts$Reports$Getsaved extends StandardParameters {
        /**
         * Required. The name of the saved report to retrieve. Format: accounts/{account\}/reports/{report\}
         */
        name?: string;
    }
    export class Resource$Accounts$Reports$Saved {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Generates a saved report.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        generate(params: Params$Resource$Accounts$Reports$Saved$Generate, options: StreamMethodOptions): GaxiosPromise<Readable>;
        generate(params?: Params$Resource$Accounts$Reports$Saved$Generate, options?: MethodOptions): GaxiosPromise<Schema$ReportResult>;
        generate(params: Params$Resource$Accounts$Reports$Saved$Generate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        generate(params: Params$Resource$Accounts$Reports$Saved$Generate, options: MethodOptions | BodyResponseCallback<Schema$ReportResult>, callback: BodyResponseCallback<Schema$ReportResult>): void;
        generate(params: Params$Resource$Accounts$Reports$Saved$Generate, callback: BodyResponseCallback<Schema$ReportResult>): void;
        generate(callback: BodyResponseCallback<Schema$ReportResult>): void;
        /**
         * Generates a csv formatted saved report.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        generateCsv(params: Params$Resource$Accounts$Reports$Saved$Generatecsv, options: StreamMethodOptions): GaxiosPromise<Readable>;
        generateCsv(params?: Params$Resource$Accounts$Reports$Saved$Generatecsv, options?: MethodOptions): GaxiosPromise<Schema$HttpBody>;
        generateCsv(params: Params$Resource$Accounts$Reports$Saved$Generatecsv, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        generateCsv(params: Params$Resource$Accounts$Reports$Saved$Generatecsv, options: MethodOptions | BodyResponseCallback<Schema$HttpBody>, callback: BodyResponseCallback<Schema$HttpBody>): void;
        generateCsv(params: Params$Resource$Accounts$Reports$Saved$Generatecsv, callback: BodyResponseCallback<Schema$HttpBody>): void;
        generateCsv(callback: BodyResponseCallback<Schema$HttpBody>): void;
        /**
         * Lists saved reports.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Accounts$Reports$Saved$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Accounts$Reports$Saved$List, options?: MethodOptions): GaxiosPromise<Schema$ListSavedReportsResponse>;
        list(params: Params$Resource$Accounts$Reports$Saved$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Accounts$Reports$Saved$List, options: MethodOptions | BodyResponseCallback<Schema$ListSavedReportsResponse>, callback: BodyResponseCallback<Schema$ListSavedReportsResponse>): void;
        list(params: Params$Resource$Accounts$Reports$Saved$List, callback: BodyResponseCallback<Schema$ListSavedReportsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListSavedReportsResponse>): void;
    }
    export interface Params$Resource$Accounts$Reports$Saved$Generate extends StandardParameters {
        /**
         * The [ISO-4217 currency code](https://en.wikipedia.org/wiki/ISO_4217) to use when reporting on monetary metrics. Defaults to the account's currency if not set.
         */
        currencyCode?: string;
        /**
         * Date range of the report, if unset the range will be considered CUSTOM.
         */
        dateRange?: string;
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        'endDate.day'?: number;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        'endDate.month'?: number;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        'endDate.year'?: number;
        /**
         * The language to use for translating report output. If unspecified, this defaults to English ("en"). If the given language is not supported, report output will be returned in English. The language is specified as an [IETF BCP-47 language code](https://en.wikipedia.org/wiki/IETF_language_tag).
         */
        languageCode?: string;
        /**
         * Required. Name of the saved report. Format: accounts/{account\}/reports/{report\}
         */
        name?: string;
        /**
         * Timezone in which to generate the report. If unspecified, this defaults to the account timezone. For more information, see [changing the time zone of your reports](https://support.google.com/adsense/answer/9830725).
         */
        reportingTimeZone?: string;
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        'startDate.day'?: number;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        'startDate.month'?: number;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        'startDate.year'?: number;
    }
    export interface Params$Resource$Accounts$Reports$Saved$Generatecsv extends StandardParameters {
        /**
         * The [ISO-4217 currency code](https://en.wikipedia.org/wiki/ISO_4217) to use when reporting on monetary metrics. Defaults to the account's currency if not set.
         */
        currencyCode?: string;
        /**
         * Date range of the report, if unset the range will be considered CUSTOM.
         */
        dateRange?: string;
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        'endDate.day'?: number;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        'endDate.month'?: number;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        'endDate.year'?: number;
        /**
         * The language to use for translating report output. If unspecified, this defaults to English ("en"). If the given language is not supported, report output will be returned in English. The language is specified as an [IETF BCP-47 language code](https://en.wikipedia.org/wiki/IETF_language_tag).
         */
        languageCode?: string;
        /**
         * Required. Name of the saved report. Format: accounts/{account\}/reports/{report\}
         */
        name?: string;
        /**
         * Timezone in which to generate the report. If unspecified, this defaults to the account timezone. For more information, see [changing the time zone of your reports](https://support.google.com/adsense/answer/9830725).
         */
        reportingTimeZone?: string;
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        'startDate.day'?: number;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        'startDate.month'?: number;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        'startDate.year'?: number;
    }
    export interface Params$Resource$Accounts$Reports$Saved$List extends StandardParameters {
        /**
         * The maximum number of reports to include in the response, used for paging. If unspecified, at most 10000 reports will be returned. The maximum value is 10000; values above 10000 will be coerced to 10000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListSavedReports` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListSavedReports` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The account which owns the collection of reports. Format: accounts/{account\}
         */
        parent?: string;
    }
    export class Resource$Accounts$Sites {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets information about the selected site.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Accounts$Sites$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Accounts$Sites$Get, options?: MethodOptions): GaxiosPromise<Schema$Site>;
        get(params: Params$Resource$Accounts$Sites$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Accounts$Sites$Get, options: MethodOptions | BodyResponseCallback<Schema$Site>, callback: BodyResponseCallback<Schema$Site>): void;
        get(params: Params$Resource$Accounts$Sites$Get, callback: BodyResponseCallback<Schema$Site>): void;
        get(callback: BodyResponseCallback<Schema$Site>): void;
        /**
         * Lists all the sites available in an account.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Accounts$Sites$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Accounts$Sites$List, options?: MethodOptions): GaxiosPromise<Schema$ListSitesResponse>;
        list(params: Params$Resource$Accounts$Sites$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Accounts$Sites$List, options: MethodOptions | BodyResponseCallback<Schema$ListSitesResponse>, callback: BodyResponseCallback<Schema$ListSitesResponse>): void;
        list(params: Params$Resource$Accounts$Sites$List, callback: BodyResponseCallback<Schema$ListSitesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListSitesResponse>): void;
    }
    export interface Params$Resource$Accounts$Sites$Get extends StandardParameters {
        /**
         * Required. Name of the site. Format: accounts/{account\}/sites/{site\}
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Sites$List extends StandardParameters {
        /**
         * The maximum number of sites to include in the response, used for paging. If unspecified, at most 10000 sites will be returned. The maximum value is 10000; values above 10000 will be coerced to 10000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListSites` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListSites` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The account which owns the collection of sites. Format: accounts/{account\}
         */
        parent?: string;
    }
    export {};
}
