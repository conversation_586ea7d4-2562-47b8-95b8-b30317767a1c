.upload-product-container {
  margin: 20px auto;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  h2 {
    margin-bottom: 20px;
    color: #007bff; // <PERSON><PERSON><PERSON>
  }

  .form-group {
    margin-bottom: 15px;

    label {
      font-weight: bold;
      color: #343a40; // <PERSON><PERSON><PERSON> chữ
    }

    input,
    textarea {
      border-radius: 5px;
      border: 1px solid #ced4da;
      &:focus {
        border-color: #007bff; // Màu viền khi focus
        box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
      }
    }
  }

  .price-label {
    margin-left: 10px;
    font-weight: bold; // Đậm
    color: #343a40; // <PERSON><PERSON>u chữ
  }

  button {
    width: 100%;
    padding: 10px;
    font-weight: bold;
  }
}

// Responsive styles

// PC (>= 1024px)
@media (min-width: 1024px) {
  .upload-product-container {
    max-width: 900px; // G<PERSON>ới hạn độ rộng cho PC lớn
    padding: 40px;
  }

  button {
    padding: 12px;
  }
}

// Tablet (>= 740px và < 1024px)
@media (min-width: 740px) and (max-width: 1023px) {
  .upload-product-container {
    max-width: 700px; // Giới hạn độ rộng cho tablet
    padding: 30px;
  }

  button {
    padding: 10px;
  }
}

// Mobile (< 740px)
@media (max-width: 739px) {
  .upload-product-container {
    padding: 15px;
  }

  button {
    padding: 8px;
  }
}
