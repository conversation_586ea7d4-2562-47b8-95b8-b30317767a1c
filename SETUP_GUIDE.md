# Hướng dẫn chạy dự án Toy Kingdom

## Tổng quan
Dự án bao gồm:
- **Frontend**: React.js (thư mục `reactjs-nhom1`)
- **Backend**: Node.js + Express + MongoDB (thư mục `toy-kingdom-backend-master`)

## <PERSON><PERSON><PERSON> cầu hệ thống
- Node.js (phiên bản 18+)
- MongoDB (chạy trên localhost:27017)
- npm hoặc yarn

## Cài đặt và chạy

### 1. Backend (API Server)

```bash
# Di chuyển vào thư mục backend
cd toy-kingdom-backend-master

# Cài đặt dependencies
npm install

# Chạy server (port 8080)
npm start
```

**Lưu ý**: Backend sẽ chạy trên port 8080 như đã cấu hình trong file `.env`

### 2. Frontend (React App)

```bash
# Di chuyển vào thư mục frontend
cd reactjs-nhom1

# Cài đặt dependencies
npm install

# Chạy development server
npm run dev
```

Frontend sẽ chạy trên port mặc định của Vite (thường là 5173)

## Cấu hình đã thực hiện

### Backend (.env)
- PORT=8080
- CONNECTION_STRING=mongodb://localhost:27017/toykingdom
- JWT secrets và expiration times

### Frontend (.env)
- VITE_API_BASE_URL=http://localhost:8080

### API Endpoints chính
- POST /login - Đăng nhập
- POST /signup - Đăng ký
- GET /product/findAll - Lấy tất cả sản phẩm
- GET /product/findOne?_id=xxx - Lấy chi tiết sản phẩm
- POST /user/cart/create - Tạo giỏ hàng
- GET /user/cart/find - Lấy giỏ hàng
- PUT /user/cart/update - Cập nhật giỏ hàng
- DELETE /user/cart/delete - Xóa giỏ hàng

## Xác thực
- Sử dụng JWT tokens (access token + refresh token)
- Header: `Authorization: Bearer <access_token>`
- Phân quyền: user, admin, owner

## Troubleshooting
1. Đảm bảo MongoDB đang chạy
2. Kiểm tra port 8080 không bị chiếm dụng
3. Xem console logs để debug lỗi API
