/* Thêm thanh cuộn riêng cho bảng */
.table-container {
  padding: 0;
  margin: 0;
  overflow-x: auto; /* Thêm thanh cuộn ngang khi cần thiết */
  -webkit-overflow-scrolling: touch; /* Thêm cuộn mượt mà cho thiết bị cảm ứng */
}

.table-container table {
  width: 100%;
  margin-bottom: 0;
  border-collapse: collapse;
}

.table-container th,
.table-container td {
  padding: 0.5rem;
  text-align: center;
  border: 1px solid #ddd;
}

/* Media query cho màn hình dưới 795px */
@media (max-width: 795px) {
  .table-container {
    overflow-x: auto; /* Bật cuộn ngang khi cần thiết */
  }

  .table-container table {
    min-width: 800px; /* Đ<PERSON>m bảo bảng không bị thu hẹp quá mức */
  }
}
