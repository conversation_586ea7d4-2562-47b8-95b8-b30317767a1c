import axiosClient from "../Api/axiosClient"; // Import axiosClient

// G<PERSON>i thông tin sản phẩm (bao gồm tên, gi<PERSON>, mô tả, độ tuổi) đến API
const uploadProductDetails = async (productDetails) => {
  const { name, price, description, suitableAge } = productDetails;

  const productData = {
    name,
    price: parseFloat(price),  // Đảm bảo giá trị price là kiểu số
    description,
    suitableAge: parseInt(suitableAge, 10),  // Đảm bảo suitableAge là kiểu số nguyên
  };

  try {
    const response = await axiosClient.post("/admin/product/create", productData);
    console.log("Sản phẩm đã được tạo:", response.data);
    return response.data; // Trả về thông tin sản phẩm vừa được tạo
  } catch (error) {
    if (error.response) {
      // In rõ chi tiết lỗi trả về từ server
      console.error("Lỗi từ server:", error.response.data);
      console.error("Status code:", error.response.status); // Mã lỗi HTTP
      console.error("Headers:", error.response.headers);  // Các header trả về từ server
    } else if (error.request) {
      console.error("Không nhận được phản hồi từ server:", error.request);
    } else {
      console.error("Lỗi khi cấu hình yêu cầu:", error.message);
    }
    alert("Có lỗi khi tạo sản phẩm!");
  }
};


export { uploadProductDetails };
