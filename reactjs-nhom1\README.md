# Bộ môn : <PERSON><PERSON><PERSON> triển ứng dụng web

# Đề tài : DỰ ÁN XÂY DỰNG TRANG WEB BÁN MÔ HÌNH ĐỒ CHƠI

# Mô tả và mục tiêu dự án

# Mô tả
Để xây dựng một trang web bán mô hình đồ chơi, nhó<PERSON> chúng em đã họp, bàn bạc với nhau để đưa ra phương pháp thực hiện một cách chỉn chủ. Dưới đây là quy trình chi tiết:
- Tìm hiểu yêu cầu của trang web thương mại điện tử cho mô hình đồ chơi
+ Xá<PERSON> định các tính năng cần có như danh mục sản phẩm, giỏ hàng, thanh toán trực tuyến, quản lý tài khoản người dùng, và khả năng tìm kiếm sản phẩm.
+ Xem xét các yếu tố đặc thù trong việc trưng bày sản ph<PERSON><PERSON> mô hình, như mô tả chi tiết, hì<PERSON> <PERSON>nh chất lượng cao từ nhiều góc, và thông tin xuất xứ hoặc loại mô hình.
- Xác định giải pháp thực hiện
+ Đưa ra hai lựa chọn: xây dựng một trang web mới hoàn toàn hoặc dựa vào các source code sẵn có để điều chỉnh. Cần phân tích thời gian và khả năng của nhóm để chọn phương pháp thích hợp.
+ Nếu chọn sử dụng source code sẵn có, xác định rõ yêu cầu mà source code đó có thể đáp ứng và những phần cần điều chỉnh hoặc mở rộng.
- Tìm kiếm và lựa chọn source code mẫu
+ Tìm các template hoặc source code từ các nền tảng như GitHub, hoặc mua template từ các trang uy tín để có bộ khung ban đầu.
+ Chọn những source code phù hợp với yêu cầu và dễ dàng điều chỉnh.
- Phân tích source code và lập danh sách điều chỉnh
+ Đọc hiểu code mẫu và xác định các chức năng có sẵn.
+ Tìm kiếm các lỗi hoặc điểm yếu cần khắc phục, đồng thời lập danh sách các phần cần thay đổi hoặc mở rộng để phù hợp với mục tiêu bán mô hình đồ chơi.
- Học hỏi từ các trang web cùng lĩnh vực
+ Nghiên cứu các trang web thương mại điện tử bán mô hình đồ chơi hoặc các sản phẩm tương tự để học hỏi điểm mạnh, như cách bố trí giao diện, chức năng tìm kiếm và phân loại, cách hiển thị sản phẩm.
+ Lên kế hoạch tạo layout bằng công cụ như Figma để hình dung trước cách giao diện sẽ trông ra sao trước khi thực hiện các thay đổi trên code.
- Chỉnh sửa và mở rộng source code
+ Thực hiện các thay đổi và mở rộng trên source code dựa trên layout đã thiết kế và các yêu cầu đã phân tích.
+ Đảm bảo tích hợp tốt các tính năng như giỏ hàng, thanh toán, đăng ký/đăng nhập, và quản lý sản phẩm.
- Kiểm tra và hoàn thiện
+ Kiểm thử trang web, đảm bảo tất cả tính năng hoạt động tốt, giao diện thân thiện với người dùng, và đáp ứng yêu cầu của một trang thương mại điện tử.
+ Tổng kết lại những gì đã đạt được, xác định những điểm cần cải thiện và đưa ra hướng phát triển trang web trong tương lai.
- Đề xuất và hướng phát triển tiếp theo
+ Đưa ra các ý tưởng phát triển, như tính năng theo dõi đơn hàng, hoặc nâng cấp trải nghiệm mua sắm.
# Mục tiêu
- Trang web Nutri Toys được nhóm chúng em nghiên cứu và phát triển nhằm thỏa mãn nhu cầu sở hữu mô hình hoạt hình được kết hợp từ hai nhu cầu chúng em nhắc ở phía trên kết hợp lại
Nutri Toys có giao diện đơn giản nhưng hiện đại, phù hợp với xu thế và giới trẻ hiện nay, có các chức năng cơ bản dành cho người dùng, khách hàng để họ có thể chọn lựa và mua sắm. Thông tin sản phẩm được hiển thị, trình bày rõ ràng, rành mạch, thuận tiện cho người có thể theo dõi. 
- Bên cạnh đó, Nutri Toys cũng hỗ trợ người bán (shop, công ty) theo dõi quá trình hàng hóa, tổng doanh thu từ những sản phẩm bán ra, thu thập thông tin khách hàng và lữu trữ những thông tin quan trọng về khách hàng cũng như hàng hóa để xử lý kịp thời.
- Đặc biệt nhất là Nutri Toys hỗ trợ người bán (shop, công ty) đặt hàng từ xa, nhằm cạnh tranh với những đối thủ trong thời đại 4.0 này, nơi mà nơi đâu cũng có giao hàng toàn quốc. Từ đó tối ưu hóa trải nghiệm mua sắm của khách hàng cũng như công tác kinh doanh của người bán (shop, công ty).
# Tác giả
- Mai Xuân Nhân - https://github.com/Mai-Xuan-Nhan
- Nguyễn Văn Tú - https://github.com/VanwTus09
- Trần Hải Dương - https://github.com/TTTHaiDuong
- Nguyễn Hoàng Huy - https://github.com/xenn34
# Kiến thức áp dụng
- Ngôn ngữ và Công nghệ sử dụng:
+ Frontend:
. HTML: Cấu trúc của trang web.
. CSS: Định dạng và bố cục của trang web.
. JavaScript: Tương tác động với người dùng và các logic phía client.
. TailwindCSS: Thư viện CSS tiện ích giúp xây dựng giao diện nhanh chóng và linh hoạt.
. ReactJS: Framework JavaScript mạnh mẽ cho xây dựng giao diện người dùng động và hiệu quả.
. Backend:
. NodeJS: Môi trường chạy JavaScript trên server, phục vụ cho việc xử lý logic và API.
. ExpressJS: Framework nhẹ và nhanh chóng cho NodeJS, dùng để xây dựng các API RESTful.
. MongoDB: Cơ sở dữ liệu NoSQL phổ biến, dễ dàng mở rộng và quản lý dữ liệu phi cấu trúc.
. MySQL: Cơ sở dữ liệu quan hệ, sử dụng SQL để quản lý và truy vấn dữ liệu.
. EJS (Optional): Nếu bạn cần render views động từ server, EJS là một lựa chọn.
+ Các công cụ và thư viện hỗ trợ:
. Vite: Công cụ xây dựng nhanh và hiện đại cho ReactJS và các framework JS khác.
. Render.com (hoặc Vercel, Heroku): Dịch vụ triển khai ứng dụng lên cloud dễ dàng và nhanh chóng.
- Các công đoạn triển khai dự án:
+ Frontend:
. Cài đặt ReactJS, cấu hình TailwindCSS cho giao diện đẹp mắt và dễ dàng tùy chỉnh.
Sử dụng các tính năng của React như Hooks và Context để quản lý trạng thái.
. Kết nối Frontend với Backend thông qua các API RESTful để lấy dữ liệu từ server.
+ Backend:
. Tạo API sử dụng ExpressJS để xử lý các yêu cầu từ phía người dùng.
. Kết nối tới MongoDB hoặc MySQL để lưu trữ và truy xuất dữ liệu.
. Xử lý xác thực người dùng, bảo mật API và các tính năng như đăng nhập, đăng ký, và phân quyền.
- Xử lý dữ liệu:
+ MongoDB: Nếu bạn chọn MongoDB, dữ liệu sẽ được lưu trữ dưới dạng document (JSON) và không cần phải xác định cấu trúc như trong MySQL.
+ MySQL: Nếu chọn MySQL, sẽ sử dụng các bảng với các khóa chính và các quan hệ giữa các bảng để lưu trữ dữ liệu.
- Triển khai:
+ Vercel/Render.com: Cả hai đều hỗ trợ triển khai ứng dụng React và Node.js nhanh chóng, bạn chỉ cần kết nối với GitHub repository và làm theo hướng dẫn.
+ Vite giúp giảm thời gian build và tối ưu hóa ứng dụng trước khi deploy lên Vercel.
- Quản lý môi trường:
+ Cấu hình file .env để quản lý các biến môi trường như API keys, database connection string khi deploy.
# Các phần liên quan
- Link github Front-end : https://github.com/VanwTus09/Toys-web 
- Link github Back-end : https://github.com/TTTHaiDuong/toy-kingdom-backend
- Link Figma : https://www.figma.com/design/Ca5lUa9vLw8PZq3gYN991W/Untitled?node-id=0-1
# Hướng dẫn sử dụng
-  Cài đặt môi trường : Node.js,MongoDB, Git
- Khởi tạo dự án : Tạo frontend bằng Vite + React bằng câu lệnh npm create vite@latest my-app -- --template react ,cd my-app,npm install
- Chia cấu trúc thư mục cho hợp lý
- Tạo thư mục backend : mkdir backend, cd backend,npm init -y
- Cài đặt các package cần thiết : npm install express mongoose dotenv cors
- Tạo models, cấu trúc backend , routes phù hợp
- Kết nối frontend với backend qua API
# Lời cảm ơn
Nhóm chúng em xin chân thành cảm ơn thầy Nguyễn Mạnh Tuấn, chúc thầy sức khỏe và tất cả những điều tốt đẹp nhất !
