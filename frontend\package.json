{"name": "toys-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fontsource/anton": "^5.1.0", "@fortawesome/fontawesome-free": "^6.6.0", "axios": "^1.7.7", "bootstrap": "^5.3.3", "chart.js": "^4.4.5", "cors": "^2.8.5", "node-sass": "^9.0.0", "nucleo": "^0.0.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "reactstrap": "^9.2.3"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.41", "react-router-dom": "^6.27.0", "sass-embedded": "^1.80.1", "tailwindcss": "^3.4.10", "vite": "^5.4.9"}}