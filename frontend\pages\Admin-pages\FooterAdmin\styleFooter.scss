footer {
  padding: 10px 0;
  background-color: #ffffff;
  /* Nền trắng */
  color: black;
  /* Ch<PERSON> đen */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  /* <PERSON><PERSON> bóng phía dưới */
  border-radius: 0;
  /* Xóa bo góc */
  border-top: 2px solid #a29b9b;
  /* Viền phía trên */
}

.social-icons a {
  font-size: 24px;
  color: rgb(2, 2, 2);
  /* Màu mặc định */
  transition: color 0.3s;
  /* Hiệu ứng chuyển màu */
}

.social-icons a:hover {
  color: #17a2b8;
  /* Màu text-info của Bootstrap */
}

/* Đổi màu chữ khi hover vào "CHÍNH SÁCH BẢO MẬT" */
.policy-link {
  transition: color 0.3s;
}

.policy-link:hover {
  color: red;
  /* Màu đỏ khi hover */
}

/* Modal */
.modal-custom {
  max-width: 600px;
  /* <PERSON><PERSON><PERSON> rộng tối đa của modal */
  width: 90%;
  /* Đảm bảo modal responsive */
}

/* Mobile: dưới 740px */
@media (max-width: 739px) {
  footer {
    text-align: center;
    /* Căn giữa nội dung footer */
  }

  .social-icons {
    justify-content: center;
    /* Căn giữa các biểu tượng xã hội */
  }

  .social-icons a {
    font-size: 20px;
    /* Giảm kích thước biểu tượng trên mobile */
  }
}

/* Tablet: từ 740px đến dưới 1024px */
@media (min-width: 740px) and (max-width: 1023px) {
  footer {
    text-align: center;
    /* Căn giữa nội dung footer */
  }

  .social-icons {
    justify-content: center;
    /* Căn giữa các biểu tượng xã hội */
  }

  .social-icons a {
    font-size: 22px;
    /* Kích thước biểu tượng phù hợp cho tablet */
  }
}

/* PC: từ 1024px trở lên */
@media (min-width: 1024px) {
  footer {
    text-align: left;
    /* Căn trái nội dung footer */
  }

  .social-icons {
    justify-content: flex-start;
    /* Để các biểu tượng ở bên trái */
  }

  .social-icons a {
    font-size: 24px;
    /* Kích thước biểu tượng lớn hơn cho PC */
  }
}

.modal-custom {
  max-width: 600px;
  width: 90%;
}

.modal-header {
  background-color: #f8f9fa;
  /* Màu nền sáng cho header */
  color: #343a40;
  /* Màu chữ tối cho dễ đọc */
  border-bottom: 1px solid #ddd;
  /* Thêm đường viền dưới */
  font-size: 1.25rem;
  padding: 15px;
}

.modal-body {
  padding: 20px;
  font-size: 1rem;
}

ol {
  list-style-type: decimal;
  padding-left: 20px;
}

ol li {
  margin-bottom: 10px;
  font-size: 1rem;
  line-height: 1.6;
}

ol li i {
  color: #17a2b8;
  /* Màu icon text-info */
  font-size: 1.2rem;
}

button {
  font-size: 1rem;
  padding: 10px 20px;
}

// New
// Biến màu sắc và font chữ (tùy chọn)
$primary-text-color: #333;
$secondary-text-color: #555;
$link-color: #007bff;
$link-hover-color: #0056b3;
$footer-background: #f8f9fa;
$section-padding: 20px 0; // Padding trên dưới cho các section

body {
  margin: 0; // Đảm bảo không có margin mặc định từ body
  font-family: Arial, sans-serif; // Chọn font chữ cơ bản
}

// --- Hotline Section ---
.hotline-section-custom {
  background-color: #fff; // Hoặc màu nền bạn muốn
  padding: $section-padding;
  border-bottom: 1px solid #e7e7e7; // Đường kẻ phân cách (tùy chọn)

  .hotline-content-wrapper {
    max-width: 1140px; // Tương đương container của Bootstrap
    margin-left: auto;
    margin-right: auto;
    padding-left: 15px; // Padding hai bên
    padding-right: 15px;
  }

  .hotline-columns-container {
    display: flex;
    flex-wrap: wrap; // Cho phép xuống hàng trên màn hình nhỏ
    gap: 20px; // Khoảng cách giữa các cột

    .hotline-column {
      flex: 1; // Chia đều không gian
      min-width: 280px; // Độ rộng tối thiểu cho mỗi cột trước khi xuống hàng

      h4 {
        font-size: 1.1em;
        color: $primary-text-color;
        margin-top: 15px;
        margin-bottom: 8px;
      }

      p,
      li {
        font-size: 0.95em;
        color: $secondary-text-color;
        line-height: 1.6;
        margin-bottom: 5px;
      }

      ul {
        list-style: none; // Bỏ dấu chấm đầu dòng
        padding-left: 0;

        li {
        
        }
      }
    }
  }

  .footer-link-custom {
    color: $link-color;
    text-decoration: none;
    font-weight: bold; // Hoặc style bạn muốn

    &:hover {
      color: $link-hover-color;
      text-decoration: underline;
    }
  }
}

// --- Footer Section ---
.footer-custom {
  background-color: $footer-background;
  padding: $section-padding;
  text-align: center; // Căn giữa nội dung mặc định

  .footer-content-wrapper {
    max-width: 1140px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 15px;
    padding-right: 15px;

    display: flex;
    flex-direction: column; // Xếp chồng trên mobile
    justify-content: space-between;
    align-items: center;
    gap: 15px; // Khoảng cách giữa payment icons và copyright

    // Media query cho màn hình lớn hơn (ví dụ: tablet trở lên)
    @media (min-width: 768px) {
      flex-direction: row; // Xếp hàng ngang trên màn hình lớn
      text-align: left; // Căn trái cho copyright text trên màn hình lớn
    }
  }

  .payment-icons-custom {
    .payment-icon {
      margin-right: 10px;
      height: 25px; // Điều chỉnh kích thước icon

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .copyright-text-custom {
    color: $secondary-text-color;
    font-size: 0.9em;
    margin: 0; // Bỏ margin mặc định của p
  }
}
