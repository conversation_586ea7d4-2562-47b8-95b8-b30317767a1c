import { useState, useEffect, useContext } from "react";
import { Context } from "../../context/context";
import Header from "../../components/layouts/header";
import Footer from "../../components/layouts/footer";

const AdminPage = () => {
  const { user } = useContext(Context);
  const [activeTab, setActiveTab] = useState("products");

  // Kiểm tra quyền admin
  useEffect(() => {
    if (user && user.role !== "admin" && user.role !== "owner") {
      alert("Bạn không có quyền truy cập trang này!");
      window.location.href = "/";
    }
  }, [user]);

  if (!user || (user.role !== "admin" && user.role !== "owner")) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">
            <PERSON><PERSON><PERSON> cậ<PERSON> bị từ chối
          </h1>
          <p className="text-gray-600">
            Bạn cần quyền admin để truy cập trang này.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Header />
      <div className="min-h-screen bg-gray-100">
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md">
            {/* Header */}
            <div className="border-b border-gray-200 px-6 py-4">
              <h1 className="text-2xl font-bold text-gray-800">
                Trang Quản Trị
              </h1>
              <p className="text-gray-600">
                Chào mừng {user.fullName} ({user.role})
              </p>
            </div>

            {/* Navigation Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                <button
                  onClick={() => setActiveTab("products")}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === "products"
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700"
                  }`}
                >
                  Quản lý Sản phẩm
                </button>
                <button
                  onClick={() => setActiveTab("users")}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === "users"
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700"
                  }`}
                >
                  Quản lý Người dùng
                </button>
                <button
                  onClick={() => setActiveTab("orders")}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === "orders"
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700"
                  }`}
                >
                  Đơn hàng
                </button>
              </nav>
            </div>

            {/* Content */}
            <div className="p-6">
              {activeTab === "products" && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Quản lý Sản phẩm</h2>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <p className="text-yellow-800">
                      Tính năng quản lý sản phẩm đang được phát triển.
                    </p>
                    <p className="text-sm text-yellow-600 mt-2">
                      API endpoints đã sẵn sàng:
                      <br />
                      • POST /admin/product/create
                      <br />
                      • PUT /admin/product/update
                      <br />
                      • DELETE /admin/product/delete
                      <br />
                      • POST /admin/product/image/create
                    </p>
                  </div>
                </div>
              )}

              {activeTab === "users" && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Quản lý Người dùng</h2>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <p className="text-yellow-800">
                      Tính năng quản lý người dùng đang được phát triển.
                    </p>
                    <p className="text-sm text-yellow-600 mt-2">
                      API endpoints đã sẵn sàng:
                      <br />
                      • GET /admin/user/findAll
                      <br />
                      • GET /admin/user/findOne
                      <br />
                      • PUT /admin/user/update
                      <br />
                      • DELETE /admin/user/delete
                    </p>
                  </div>
                </div>
              )}

              {activeTab === "orders" && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Quản lý Đơn hàng</h2>
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <p className="text-blue-800">
                      Tính năng quản lý đơn hàng sẽ được thêm vào sau.
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Admin Info */}
            <div className="border-t border-gray-200 px-6 py-4 bg-gray-50">
              <h3 className="text-lg font-medium text-gray-800 mb-2">
                Thông tin Admin
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Tài khoản test admin:</span>
                  <br />
                  Email: <EMAIL>
                  <br />
                  Password: user123
                </div>
                <div>
                  <span className="font-medium">Tài khoản owner:</span>
                  <br />
                  Email: <EMAIL>
                  <br />
                  Password: user123
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default AdminPage;
