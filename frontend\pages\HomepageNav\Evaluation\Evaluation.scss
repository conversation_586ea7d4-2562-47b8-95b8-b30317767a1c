.product-card {
  background-color: #fff;
  border-radius: 10px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;

  .description {
    font-size: 1rem;
    color: #6c757d;
  }

  .product-image {
    width: 100%;
    max-width: 200px;
    height: auto;
    object-fit: cover;
    border-radius: 8px;
  }

  .btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    padding: 0.5rem 1rem; // Thêm padding cho button
    border-radius: 5px;
    font-size: 1rem;

    &:hover {
      background-color: #0056b3;
      border-color: #004085;
    }
  }

  .modal-body h5 {
    font-weight: bold;
    margin-bottom: 1rem;
  }
}

@media (max-width: 739px) {
  .product-card {
    padding: 1rem;
  }

  .product-image {
    max-width: 150px;
  }

  .btn-primary {
    width: 100%; // Button chiếm toàn bộ chiều rộng trên màn hình nhỏ
  }
}
.product-description {
  display: block;
  overflow: hidden; /* Ẩn phần vượt quá */
  text-overflow: ellipsis; /* <PERSON><PERSON>n thị dấu ba chấm */
  white-space: normal; /* Cho phép văn bản xuống dòng */
  line-height: 1.4; /* Đảm bảo các dòng có chiều cao hợp lý */
  max-height: 4.2em; /* Giới hạn 3 dòng với line-height 1.4 */
}
