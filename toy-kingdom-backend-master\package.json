{"name": "backend", "version": "1.0.0", "main": "index.js", "type": "module", "engines": {"node": "20.x"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon --exec babel-node index.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@infobip-api/sdk": "^0.3.2", "bcryptjs": "^2.4.3", "connect": "^3.7.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.21.0", "express-session": "^1.18.1", "formidable": "^3.5.2", "google-auth-library": "^9.14.2", "googleapis": "^144.0.0", "jsonwebtoken": "^9.0.2", "mailersend": "^2.3.0", "mkdirp": "^3.0.1", "mongoose": "^8.8.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.3", "sequelize": "^6.37.5", "validator": "^13.12.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/node": "^7.25.0", "@babel/preset-env": "^7.25.4", "nodemon": "^3.1.7", "sequelize-cli": "^6.6.2"}}