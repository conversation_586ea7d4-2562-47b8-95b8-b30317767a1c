import React from "react";
import "../FooterAdmin/styleFooter.scss"; // Đ<PERSON><PERSON> bảo file này tồn tại và bạn sẽ viết CSS ở đây

const AdminFooter = () => {
  return (
    <>
      <div className="hotline-section-custom"> {/* Đổi tên class để tránh xung đột nếu có */}
        <div className="hotline-content-wrapper"> {/* Wrapper để căn giữa nội dung */}
          <div className="hotline-columns-container"> {/* Container cho hai cột */}
            <div className="hotline-column">
              <h4>Hotline</h4>
              <p>098.7654.321</p>
              <h4>Mail</h4>
              <p><EMAIL></p>
              <h4>
                <a href="/sales-policy" className="footer-link-custom">
                  CHÍNH SÁCH BÁN HÀNG
                </a>
              </h4>
              <h4>
                <a href="/shopping-guide" className="footer-link-custom">
                  HƯỚNG DẪN MUA HÀNG
                </a>
              </h4>
            </div>
            <div className="hotline-column">
              <h4>TP. HỒ CHÍ MINH (9h30 - 22h)</h4>
              <ul>
                <li>92 Hồ Tùng Mậu, P.Bến Nghé, Q.1</li>
                <li>459E Nguyễn Đình Chiểu, P.5, Q.3 (ngã tư Cao Thắng)</li>
                <li>708 Sư Vạn Hạnh, P.12, Q.10 (đối diện chéo Van Hạnh Mall)</li>
                <li>232 Phan Xích Long, P.7, Q.Phú Nhuận</li>
              </ul>
              <h4>HÀ NỘI (9h - 22h)</h4>
              <ul>
                <li>81 Bà Triệu, Hai Bà Trưng</li>
                <li>241 Chùa Bộc, Đống Đa</li>
                <li>60 Trần Đại Nghĩa, Hai Bà Trưng</li>
                <li>226 Nguyễn Trãi, Nam Từ Liêm (gần BH Hà Nội)</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div className="footer-custom"> {/* Đổi tên class */}
        <div className="footer-content-wrapper"> {/* Wrapper để căn giữa và sắp xếp nội dung */}
          <div className="payment-icons-custom">
            <img
              src="https://via.placeholder.com/50x20/007bff/fff?Text=PayPal"
              alt="PayPal"
              className="payment-icon"
            />
            <img
              src="https://via.placeholder.com/50x20/ffc107/000?Text=Visa"
              alt="Visa"
              className="payment-icon"
            />
            <img
              src="https://via.placeholder.com/50x20/28a745/fff?Text=Mastercard"
              alt="Mastercard"
              className="payment-icon"
            />
          </div>
          <p className="copyright-text-custom">
            Copyright &copy; {new Date().getFullYear()} URUHUT All Rights Reserved
          </p>
        </div>
      </div>
    </>
  );
};

export default AdminFooter;