import React, { useState } from "react";
import {
  Button, // Vẫn sử dụng Button của reactstrap cho các nút trong <PERSON>dal
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  ModalFooter,
} from "reactstrap";
// <PERSON><PERSON><PERSON> sử bạn sẽ tạo một file SCSS mới hoặc điều chỉnh file hiện có cho cấu trúc mới
// import "../CardProduct/NewCardProductStyles.scss"; // Ví dụ: một file SCSS mới
import { deleteProduct, updateProduct } from "../../../Api/product"; // Import hàm xóa và chỉnh sửa sản phẩm

const CardProduct = ({ product, onDeleteSuccess, onUpdateSuccess }) => {
  const [modal, setModal] = useState(false); // Modal xóa
  const [modalEdit, setModalEdit] = useState(false); // Modal chỉnh sửa
  const [editAttributes, setEditAttributes] = useState({
    name: product.name,
    price: product.price,
    description: product.description,
  });

  // Đổi trạng thái của modal xóa
  const toggle = () => setModal(!modal);
  // Đổi trạng thái của modal chỉnh sửa
  const toggleEdit = () => {
    if (!modalEdit) {
      // Khi mở modal edit, đặt lại editAttributes về giá trị hiện tại của sản phẩm
      setEditAttributes({
        name: product.name,
        price: product.price,
        description: product.description,
      });
    }
    setModalEdit(!modalEdit);
  };

  // Hàm xử lý xóa sản phẩm
  const handleDelete = async () => {
    try {
      const result = await deleteProduct([product._id]); // Xóa sản phẩm từ API
      console.log("Sản phẩm đã bị xóa:", result);
      onDeleteSuccess(product._id); // Gọi hàm callback sau khi xóa thành công
      toggle(); // Đóng modal xóa
    } catch (error) {
      console.error("Lỗi khi xóa sản phẩm:", error);
    }
  };

  // Hàm xử lý thay đổi dữ liệu chỉnh sửa khi người dùng nhập
  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditAttributes((prev) => ({ ...prev, [name]: value }));
  };

  // Hàm xử lý cập nhật sản phẩm
  const handleUpdate = async () => {
    if (
      editAttributes.name === product.name &&
      String(editAttributes.price) === String(product.price) && // So sánh giá dưới dạng chuỗi hoặc số nhất quán
      editAttributes.description === product.description
    ) {
      toggleEdit();
      return;
    }

    try {
      // Đảm bảo price là số trước khi gửi
      const payload = {
        ...editAttributes,
        price: parseFloat(editAttributes.price) || 0, // Chuyển đổi giá trị sang số
      };
      const result = await updateProduct(product._id, payload);
      console.log("Sản phẩm đã được cập nhật:", result);
      onUpdateSuccess(product._id, payload); // Gọi hàm callback sau khi cập nhật thành công
      toggleEdit(); // Đóng modal chỉnh sửa
    } catch (error) {
      console.error("Lỗi khi cập nhật sản phẩm:", error);
    }
  };

  const imageUrl =
    product.images && product.images[0]?.buffer
      ? `data:${product.images[0].mimetype};base64,${product.images[0].buffer}`
      : "https://pos.nvncdn.com/71a8b2-3946/ps/20250327_YNHCaRLFSn.jpeg"; // Hình ảnh mặc định

  return (
    <div className="col"> {/* Hoặc class tương ứng với grid system bạn dùng, ví dụ: col-md-4 mb-3 */}
      <div className="product-card shadow-sm rounded"> {/* Thêm shadow và rounded nếu muốn */}
        <img
          src={imageUrl}
          alt={product.name}
          className="product-image"
        />
        <div className="product-details">
          {product._id && <p><strong>ID sản phẩm:</strong> {product._id}</p>}
          <p><strong>Tên sản phẩm:</strong> {product.name}</p>
          <p>
            <strong>Mô tả:</strong>{" "}
            {product.description
              ? product.description.substring(0, 100) + (product.description.length > 100 ? "..." : "")
              : "Không có mô tả"}
          </p>
          <p>
            <strong>Giá:</strong>{" "}
            {product.price !== undefined ? product.price.toLocaleString() : "N/A"} VNĐ
          </p>
          <div className="product-actions">
            {/* Nút "Còn hàng" có thể thêm vào đây nếu cần với logic tương ứng */}
            {/* <button className="btn btn-warning btn-sm me-2">Còn hàng</button> */}
            <button
              className="btn btn-secondary btn-sm me-2"
              onClick={toggleEdit}
            >
              Chỉnh sửa
            </button>
            <button
              className="btn btn-danger btn-sm"
              onClick={toggle}
            >
              Xoá sản phẩm
            </button>
          </div>
        </div>
      </div>

      {/* Modal xác nhận xóa (sử dụng reactstrap Modal) */}
      <Modal isOpen={modal} toggle={toggle}>
        <ModalHeader toggle={toggle}>Xác nhận xóa</ModalHeader>
        <ModalBody>
          Bạn có chắc chắn muốn xóa sản phẩm <strong>{product.name}</strong>{" "}
          không?
        </ModalBody>
        <ModalFooter>
          <Button color="secondary" onClick={toggle}>
            Hủy
          </Button>
          <Button color="danger" onClick={handleDelete}>
            Xóa
          </Button>
        </ModalFooter>
      </Modal>

      {/* Modal chỉnh sửa (sử dụng reactstrap Modal) */}
      <Modal isOpen={modalEdit} toggle={toggleEdit}>
        <ModalHeader toggle={toggleEdit}>Chỉnh sửa sản phẩm</ModalHeader>
        <ModalBody>
          {/* Các input nên được bọc trong form group hoặc div để dễ dàng tạo kiểu */}
          <div className="mb-3">
            <label htmlFor={`productName-${product._id}`} className="form-label">Tên sản phẩm:</label>
            <input
              type="text"
              id={`productName-${product._id}`}
              className="form-control" // Sử dụng class của Bootstrap cho input
              name="name"
              value={editAttributes.name}
              onChange={handleEditChange}
              placeholder="Nhập tên sản phẩm"
            />
          </div>
          <div className="mb-3">
            <label htmlFor={`productPrice-${product._id}`} className="form-label">Giá:</label>
            <input
              type="number"
              id={`productPrice-${product._id}`}
              className="form-control"
              name="price"
              value={editAttributes.price}
              onChange={handleEditChange}
              placeholder="Nhập giá sản phẩm"
            />
          </div>
          <div className="mb-3">
            <label htmlFor={`productDescription-${product._id}`} className="form-label">Mô tả:</label>
            <textarea
              id={`productDescription-${product._id}`}
              className="form-control"
              name="description"
              value={editAttributes.description}
              onChange={handleEditChange}
              placeholder="Nhập mô tả sản phẩm"
              rows="3"
            />
          </div>
        </ModalBody>
        <ModalFooter>
          <Button color="secondary" onClick={toggleEdit}>
            Hủy
          </Button>
          <Button color="primary" onClick={handleUpdate}>
            Cập nhật
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default CardProduct;