/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace translate_v3beta1 {
    export interface Options extends GlobalOptions {
        version: 'v3beta1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud Translation API
     *
     * Integrates text translation into your website or application.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const translate = google.translate('v3beta1');
     * ```
     */
    export class Translate {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Input configuration for BatchTranslateDocument request.
     */
    export interface Schema$BatchDocumentInputConfig {
        /**
         * Google Cloud Storage location for the source input. This can be a single file (for example, `gs://translation-test/input.docx`) or a wildcard (for example, `gs://translation-test/x`). File mime type is determined based on extension. Supported mime type includes: - `pdf`, application/pdf - `docx`, application/vnd.openxmlformats-officedocument.wordprocessingml.document - `pptx`, application/vnd.openxmlformats-officedocument.presentationml.presentation - `xlsx`, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet The max file size to support for `.docx`, `.pptx` and `.xlsx` is 100MB. The max file size to support for `.pdf` is 1GB and the max page limit is 1000 pages. The max file size to support for all input documents is 1GB.
         */
        gcsSource?: Schema$GcsSource;
    }
    /**
     * Output configuration for BatchTranslateDocument request.
     */
    export interface Schema$BatchDocumentOutputConfig {
        /**
         * Google Cloud Storage destination for output content. For every single input document (for example, gs://a/b/c.[extension]), we generate at most 2 * n output files. (n is the # of target_language_codes in the BatchTranslateDocumentRequest). While the input documents are being processed, we write/update an index file `index.csv` under `gcs_destination.output_uri_prefix` (for example, gs://translation_output/index.csv) The index file is generated/updated as new files are being translated. The format is: input_document,target_language_code,translation_output,error_output, glossary_translation_output,glossary_error_output `input_document` is one file we matched using gcs_source.input_uri. `target_language_code` is provided in the request. `translation_output` contains the translations. (details provided below) `error_output` contains the error message during processing of the file. Both translations_file and errors_file could be empty strings if we have no content to output. `glossary_translation_output` and `glossary_error_output` are the translated output/error when we apply glossaries. They could also be empty if we have no content to output. Once a row is present in index.csv, the input/output matching never changes. Callers should also expect all the content in input_file are processed and ready to be consumed (that is, no partial output file is written). Since index.csv will be keeping updated during the process, please make sure there is no custom retention policy applied on the output bucket that may avoid file updating. (https://cloud.google.com/storage/docs/bucket-lock#retention-policy) The naming format of translation output files follows (for target language code [trg]): `translation_output`: `gs://translation_output/a_b_c_[trg]_translation.[extension]` `glossary_translation_output`: `gs://translation_test/a_b_c_[trg]_glossary_translation.[extension]`. The output document will maintain the same file format as the input document. The naming format of error output files follows (for target language code [trg]): `error_output`: `gs://translation_test/a_b_c_[trg]_errors.txt` `glossary_error_output`: `gs://translation_test/a_b_c_[trg]_glossary_translation.txt` The error output is a txt file containing error details.
         */
        gcsDestination?: Schema$GcsDestination;
    }
    /**
     * The BatchTranslateDocument request.
     */
    export interface Schema$BatchTranslateDocumentRequest {
        /**
         * Optional. This flag is to support user customized attribution. If not provided, the default is `Machine Translated by Google`. Customized attribution should follow rules in https://cloud.google.com/translate/attribution#attribution_and_logos
         */
        customizedAttribution?: string | null;
        /**
         * Optional. If true, enable auto rotation correction in DVS.
         */
        enableRotationCorrection?: boolean | null;
        /**
         * Optional. If true, use the text removal server to remove the shadow text on background image for native pdf translation. Shadow removal feature can only be enabled when is_translate_native_pdf_only: false && pdf_native_only: false
         */
        enableShadowRemovalNativePdf?: boolean | null;
        /**
         * Optional. File format conversion map to be applied to all input files. Map's key is the original mime_type. Map's value is the target mime_type of translated documents. Supported file format conversion includes: - `application/pdf` to `application/vnd.openxmlformats-officedocument.wordprocessingml.document` If nothing specified, output files will be in the same format as the original file.
         */
        formatConversions?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. Glossaries to be applied. It's keyed by target language code.
         */
        glossaries?: {
            [key: string]: Schema$TranslateTextGlossaryConfig;
        } | null;
        /**
         * Required. Input configurations. The total number of files matched should be <= 100. The total content size to translate should be <= 100M Unicode codepoints. The files must use UTF-8 encoding.
         */
        inputConfigs?: Schema$BatchDocumentInputConfig[];
        /**
         * Optional. The models to use for translation. Map's key is target language code. Map's value is the model name. Value can be a built-in general model, or an AutoML Translation model. The value format depends on model type: - AutoML Translation models: `projects/{project-number-or-id\}/locations/{location-id\}/models/{model-id\}` - General (built-in) models: `projects/{project-number-or-id\}/locations/{location-id\}/models/general/nmt`, If the map is empty or a specific model is not requested for a language pair, then default google model (nmt) is used.
         */
        models?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Output configuration. If 2 input configs match to the same file (that is, same input path), we don't generate output for duplicate inputs.
         */
        outputConfig?: Schema$BatchDocumentOutputConfig;
        /**
         * Required. The BCP-47 language code of the input document if known, for example, "en-US" or "sr-Latn". Supported language codes are listed in [Language Support](https://cloud.google.com/translate/docs/languages).
         */
        sourceLanguageCode?: string | null;
        /**
         * Required. The BCP-47 language code to use for translation of the input document. Specify up to 10 language codes here.
         */
        targetLanguageCodes?: string[] | null;
    }
    /**
     * The batch translation request.
     */
    export interface Schema$BatchTranslateTextRequest {
        /**
         * Optional. Glossaries to be applied for translation. It's keyed by target language code.
         */
        glossaries?: {
            [key: string]: Schema$TranslateTextGlossaryConfig;
        } | null;
        /**
         * Required. Input configurations. The total number of files matched should be <= 100. The total content size should be <= 100M Unicode codepoints. The files must use UTF-8 encoding.
         */
        inputConfigs?: Schema$InputConfig[];
        /**
         * Optional. The labels with user-defined metadata for the request. Label keys and values can be no longer than 63 characters (Unicode codepoints), can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter. See https://cloud.google.com/translate/docs/labels for more information.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. The models to use for translation. Map's key is target language code. Map's value is model name. Value can be a built-in general model, or an AutoML Translation model. The value format depends on model type: - AutoML Translation models: `projects/{project-number-or-id\}/locations/{location-id\}/models/{model-id\}` - General (built-in) models: `projects/{project-number-or-id\}/locations/{location-id\}/models/general/nmt`, If the map is empty or a specific model is not requested for a language pair, then default google model (nmt) is used.
         */
        models?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Output configuration. If 2 input configs match to the same file (that is, same input path), we don't generate output for duplicate inputs.
         */
        outputConfig?: Schema$OutputConfig;
        /**
         * Required. Source language code.
         */
        sourceLanguageCode?: string | null;
        /**
         * Required. Specify up to 10 language codes here.
         */
        targetLanguageCodes?: string[] | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * The response message for language detection.
     */
    export interface Schema$DetectedLanguage {
        /**
         * The confidence of the detection result for this language.
         */
        confidence?: number | null;
        /**
         * The BCP-47 language code of source content in the request, detected automatically.
         */
        languageCode?: string | null;
    }
    /**
     * The request message for language detection.
     */
    export interface Schema$DetectLanguageRequest {
        /**
         * The content of the input stored as a string.
         */
        content?: string | null;
        /**
         * Optional. The labels with user-defined metadata for the request. Label keys and values can be no longer than 63 characters (Unicode codepoints), can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter. See https://cloud.google.com/translate/docs/labels for more information.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. The format of the source text, for example, "text/html", "text/plain". If left blank, the MIME type defaults to "text/html".
         */
        mimeType?: string | null;
        /**
         * Optional. The language detection model to be used. Format: `projects/{project-number-or-id\}/locations/{location-id\}/models/language-detection/{model-id\}` Only one language detection model is currently supported: `projects/{project-number-or-id\}/locations/{location-id\}/models/language-detection/default`. If not specified, the default model is used.
         */
        model?: string | null;
    }
    /**
     * The response message for language detection.
     */
    export interface Schema$DetectLanguageResponse {
        /**
         * A list of detected languages sorted by detection confidence in descending order. The most probable language first.
         */
        languages?: Schema$DetectedLanguage[];
    }
    /**
     * A document translation request input config.
     */
    export interface Schema$DocumentInputConfig {
        /**
         * Document's content represented as a stream of bytes.
         */
        content?: string | null;
        /**
         * Google Cloud Storage location. This must be a single file. For example: gs://example_bucket/example_file.pdf
         */
        gcsSource?: Schema$GcsSource;
        /**
         * Specifies the input document's mime_type. If not specified it will be determined using the file extension for gcs_source provided files. For a file provided through bytes content the mime_type must be provided. Currently supported mime types are: - application/pdf - application/vnd.openxmlformats-officedocument.wordprocessingml.document - application/vnd.openxmlformats-officedocument.presentationml.presentation - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
         */
        mimeType?: string | null;
    }
    /**
     * A document translation request output config.
     */
    export interface Schema$DocumentOutputConfig {
        /**
         * Optional. Google Cloud Storage destination for the translation output, e.g., `gs://my_bucket/my_directory/`. The destination directory provided does not have to be empty, but the bucket must exist. If a file with the same name as the output file already exists in the destination an error will be returned. For a DocumentInputConfig.contents provided document, the output file will have the name "output_[trg]_translations.[ext]", where - [trg] corresponds to the translated file's language code, - [ext] corresponds to the translated file's extension according to its mime type. For a DocumentInputConfig.gcs_uri provided document, the output file will have a name according to its URI. For example: an input file with URI: `gs://a/b/c.[extension]` stored in a gcs_destination bucket with name "my_bucket" will have an output URI: `gs://my_bucket/a_b_c_[trg]_translations.[ext]`, where - [trg] corresponds to the translated file's language code, - [ext] corresponds to the translated file's extension according to its mime type. If the document was directly provided through the request, then the output document will have the format: `gs://my_bucket/translated_document_[trg]_translations.[ext]`, where - [trg] corresponds to the translated file's language code, - [ext] corresponds to the translated file's extension according to its mime type. If a glossary was provided, then the output URI for the glossary translation will be equal to the default output URI but have `glossary_translations` instead of `translations`. For the previous example, its glossary URI would be: `gs://my_bucket/a_b_c_[trg]_glossary_translations.[ext]`. Thus the max number of output files will be 2 (Translated document, Glossary translated document). Callers should expect no partial outputs. If there is any error during document translation, no output will be stored in the Cloud Storage bucket.
         */
        gcsDestination?: Schema$GcsDestination;
        /**
         * Optional. Specifies the translated document's mime_type. If not specified, the translated file's mime type will be the same as the input file's mime type. Currently only support the output mime type to be the same as input mime type. - application/pdf - application/vnd.openxmlformats-officedocument.wordprocessingml.document - application/vnd.openxmlformats-officedocument.presentationml.presentation - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
         */
        mimeType?: string | null;
    }
    /**
     * A translated document message.
     */
    export interface Schema$DocumentTranslation {
        /**
         * The array of translated documents. It is expected to be size 1 for now. We may produce multiple translated documents in the future for other type of file formats.
         */
        byteStreamOutputs?: string[] | null;
        /**
         * The detected language for the input document. If the user did not provide the source language for the input document, this field will have the language code automatically detected. If the source language was passed, auto-detection of the language does not occur and this field is empty.
         */
        detectedLanguageCode?: string | null;
        /**
         * The translated document's mime type.
         */
        mimeType?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * The Google Cloud Storage location for the output content.
     */
    export interface Schema$GcsDestination {
        /**
         * Required. There must be no files under 'output_uri_prefix'. 'output_uri_prefix' must end with "/" and start with "gs://", otherwise an INVALID_ARGUMENT (400) error is returned.
         */
        outputUriPrefix?: string | null;
    }
    /**
     * The Google Cloud Storage location for the input content.
     */
    export interface Schema$GcsSource {
        /**
         * Required. Source data URI. For example, `gs://my_bucket/my_object`.
         */
        inputUri?: string | null;
    }
    /**
     * Represents a glossary built from user provided data.
     */
    export interface Schema$Glossary {
        /**
         * Output only. When the glossary creation was finished.
         */
        endTime?: string | null;
        /**
         * Output only. The number of entries defined in the glossary.
         */
        entryCount?: number | null;
        /**
         * Required. Provides examples to build the glossary from. Total glossary must not exceed 10M Unicode codepoints.
         */
        inputConfig?: Schema$GlossaryInputConfig;
        /**
         * Used with equivalent term set glossaries.
         */
        languageCodesSet?: Schema$LanguageCodesSet;
        /**
         * Used with unidirectional glossaries.
         */
        languagePair?: Schema$LanguageCodePair;
        /**
         * Required. The resource name of the glossary. Glossary names have the form `projects/{project-number-or-id\}/locations/{location-id\}/glossaries/{glossary-id\}`.
         */
        name?: string | null;
        /**
         * Output only. When CreateGlossary was called.
         */
        submitTime?: string | null;
    }
    /**
     * Input configuration for glossaries.
     */
    export interface Schema$GlossaryInputConfig {
        /**
         * Required. Google Cloud Storage location of glossary data. File format is determined based on the filename extension. API returns [google.rpc.Code.INVALID_ARGUMENT] for unsupported URI-s and file formats. Wildcards are not allowed. This must be a single file in one of the following formats: For unidirectional glossaries: - TSV/CSV (`.tsv`/`.csv`): 2 column file, tab- or comma-separated. The first column is source text. The second column is target text. The file must not contain headers. That is, the first row is data, not column names. - TMX (`.tmx`): TMX file with parallel data defining source/target term pairs. For equivalent term sets glossaries: - CSV (`.csv`): Multi-column CSV file defining equivalent glossary terms in multiple languages. See documentation for more information - [glossaries](https://cloud.google.com/translate/docs/advanced/glossary).
         */
        gcsSource?: Schema$GcsSource;
    }
    /**
     * Input configuration for BatchTranslateText request.
     */
    export interface Schema$InputConfig {
        /**
         * Required. Google Cloud Storage location for the source input. This can be a single file (for example, `gs://translation-test/input.tsv`) or a wildcard (for example, `gs://translation-test/x`). If a file extension is `.tsv`, it can contain either one or two columns. The first column (optional) is the id of the text request. If the first column is missing, we use the row number (0-based) from the input file as the ID in the output file. The second column is the actual text to be translated. We recommend each row be <= 10K Unicode codepoints, otherwise an error might be returned. Note that the input tsv must be RFC 4180 compliant. You could use https://github.com/Clever/csvlint to check potential formatting errors in your tsv file. csvlint --delimiter='\t' your_input_file.tsv The other supported file extensions are `.txt` or `.html`, which is treated as a single large chunk of text.
         */
        gcsSource?: Schema$GcsSource;
        /**
         * Optional. Can be "text/plain" or "text/html". For `.tsv`, "text/html" is used if mime_type is missing. For `.html`, this field must be "text/html" or empty. For `.txt`, this field must be "text/plain" or empty.
         */
        mimeType?: string | null;
    }
    /**
     * Used with unidirectional glossaries.
     */
    export interface Schema$LanguageCodePair {
        /**
         * Required. The BCP-47 language code of the input text, for example, "en-US". Expected to be an exact match for GlossaryTerm.language_code.
         */
        sourceLanguageCode?: string | null;
        /**
         * Required. The BCP-47 language code for translation output, for example, "zh-CN". Expected to be an exact match for GlossaryTerm.language_code.
         */
        targetLanguageCode?: string | null;
    }
    /**
     * Used with equivalent term set glossaries.
     */
    export interface Schema$LanguageCodesSet {
        /**
         * The BCP-47 language code(s) for terms defined in the glossary. All entries are unique. The list contains at least two entries. Expected to be an exact match for GlossaryTerm.language_code.
         */
        languageCodes?: string[] | null;
    }
    /**
     * Response message for ListGlossaries.
     */
    export interface Schema$ListGlossariesResponse {
        /**
         * The list of glossaries for a project.
         */
        glossaries?: Schema$Glossary[];
        /**
         * A token to retrieve a page of results. Pass this value in the [ListGlossariesRequest.page_token] field in the subsequent call to `ListGlossaries` method to retrieve the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Output configuration for BatchTranslateText request.
     */
    export interface Schema$OutputConfig {
        /**
         * Google Cloud Storage destination for output content. For every single input file (for example, gs://a/b/c.[extension]), we generate at most 2 * n output files. (n is the # of target_language_codes in the BatchTranslateTextRequest). Output files (tsv) generated are compliant with RFC 4180 except that record delimiters are '\n' instead of '\r\n'. We don't provide any way to change record delimiters. While the input files are being processed, we write/update an index file 'index.csv' under 'output_uri_prefix' (for example, gs://translation-test/index.csv) The index file is generated/updated as new files are being translated. The format is: input_file,target_language_code,translations_file,errors_file, glossary_translations_file,glossary_errors_file input_file is one file we matched using gcs_source.input_uri. target_language_code is provided in the request. translations_file contains the translations. (details provided below) errors_file contains the errors during processing of the file. (details below). Both translations_file and errors_file could be empty strings if we have no content to output. glossary_translations_file and glossary_errors_file are always empty strings if the input_file is tsv. They could also be empty if we have no content to output. Once a row is present in index.csv, the input/output matching never changes. Callers should also expect all the content in input_file are processed and ready to be consumed (that is, no partial output file is written). Since index.csv will be keeping updated during the process, please make sure there is no custom retention policy applied on the output bucket that may avoid file updating. (https://cloud.google.com/storage/docs/bucket-lock#retention-policy) The format of translations_file (for target language code 'trg') is: `gs://translation_test/a_b_c_'trg'_translations.[extension]` If the input file extension is tsv, the output has the following columns: Column 1: ID of the request provided in the input, if it's not provided in the input, then the input row number is used (0-based). Column 2: source sentence. Column 3: translation without applying a glossary. Empty string if there is an error. Column 4 (only present if a glossary is provided in the request): translation after applying the glossary. Empty string if there is an error applying the glossary. Could be same string as column 3 if there is no glossary applied. If input file extension is a txt or html, the translation is directly written to the output file. If glossary is requested, a separate glossary_translations_file has format of `gs://translation_test/a_b_c_'trg'_glossary_translations.[extension]` The format of errors file (for target language code 'trg') is: `gs://translation_test/a_b_c_'trg'_errors.[extension]` If the input file extension is tsv, errors_file contains the following: Column 1: ID of the request provided in the input, if it's not provided in the input, then the input row number is used (0-based). Column 2: source sentence. Column 3: Error detail for the translation. Could be empty. Column 4 (only present if a glossary is provided in the request): Error when applying the glossary. If the input file extension is txt or html, glossary_error_file will be generated that contains error details. glossary_error_file has format of `gs://translation_test/a_b_c_'trg'_glossary_errors.[extension]`
         */
        gcsDestination?: Schema$GcsDestination;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * A single supported language response corresponds to information related to one supported language.
     */
    export interface Schema$SupportedLanguage {
        /**
         * Human readable name of the language localized in the display language specified in the request.
         */
        displayName?: string | null;
        /**
         * Supported language code, generally consisting of its ISO 639-1 identifier, for example, 'en', 'ja'. In certain cases, BCP-47 codes including language and region identifiers are returned (for example, 'zh-TW' and 'zh-CN')
         */
        languageCode?: string | null;
        /**
         * Can be used as source language.
         */
        supportSource?: boolean | null;
        /**
         * Can be used as target language.
         */
        supportTarget?: boolean | null;
    }
    /**
     * The response message for discovering supported languages.
     */
    export interface Schema$SupportedLanguages {
        /**
         * A list of supported language responses. This list contains an entry for each language the Translation API supports.
         */
        languages?: Schema$SupportedLanguage[];
    }
    /**
     * A document translation request.
     */
    export interface Schema$TranslateDocumentRequest {
        /**
         * Optional. This flag is to support user customized attribution. If not provided, the default is `Machine Translated by Google`. Customized attribution should follow rules in https://cloud.google.com/translate/attribution#attribution_and_logos
         */
        customizedAttribution?: string | null;
        /**
         * Required. Input configurations.
         */
        documentInputConfig?: Schema$DocumentInputConfig;
        /**
         * Optional. Output configurations. Defines if the output file should be stored within Cloud Storage as well as the desired output format. If not provided the translated file will only be returned through a byte-stream and its output mime type will be the same as the input file's mime type.
         */
        documentOutputConfig?: Schema$DocumentOutputConfig;
        /**
         * Optional. If true, enable auto rotation correction in DVS.
         */
        enableRotationCorrection?: boolean | null;
        /**
         * Optional. If true, use the text removal server to remove the shadow text on background image for native pdf translation. Shadow removal feature can only be enabled when is_translate_native_pdf_only: false && pdf_native_only: false
         */
        enableShadowRemovalNativePdf?: boolean | null;
        /**
         * Optional. Glossary to be applied. The glossary must be within the same region (have the same location-id) as the model, otherwise an INVALID_ARGUMENT (400) error is returned.
         */
        glossaryConfig?: Schema$TranslateTextGlossaryConfig;
        /**
         * Optional. is_translate_native_pdf_only field for external customers. If true, the page limit of online native pdf translation is 300 and only native pdf pages will be translated.
         */
        isTranslateNativePdfOnly?: boolean | null;
        /**
         * Optional. The labels with user-defined metadata for the request. Label keys and values can be no longer than 63 characters (Unicode codepoints), can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter. See https://cloud.google.com/translate/docs/advanced/labels for more information.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. The `model` type requested for this translation. The format depends on model type: - AutoML Translation models: `projects/{project-number-or-id\}/locations/{location-id\}/models/{model-id\}` - General (built-in) models: `projects/{project-number-or-id\}/locations/{location-id\}/models/general/nmt`, If not provided, the default Google model (NMT) will be used for translation.
         */
        model?: string | null;
        /**
         * Optional. The BCP-47 language code of the input document if known, for example, "en-US" or "sr-Latn". Supported language codes are listed in Language Support. If the source language isn't specified, the API attempts to identify the source language automatically and returns the source language within the response. Source language must be specified if the request contains a glossary or a custom model.
         */
        sourceLanguageCode?: string | null;
        /**
         * Required. The BCP-47 language code to use for translation of the input document, set to one of the language codes listed in Language Support.
         */
        targetLanguageCode?: string | null;
    }
    /**
     * A translated document response message.
     */
    export interface Schema$TranslateDocumentResponse {
        /**
         * Translated document.
         */
        documentTranslation?: Schema$DocumentTranslation;
        /**
         * The `glossary_config` used for this translation.
         */
        glossaryConfig?: Schema$TranslateTextGlossaryConfig;
        /**
         * The document's translation output if a glossary is provided in the request. This can be the same as [TranslateDocumentResponse.document_translation] if no glossary terms apply.
         */
        glossaryDocumentTranslation?: Schema$DocumentTranslation;
        /**
         * Only present when 'model' is present in the request. 'model' is normalized to have a project number. For example: If the 'model' field in TranslateDocumentRequest is: `projects/{project-id\}/locations/{location-id\}/models/general/nmt` then `model` here would be normalized to `projects/{project-number\}/locations/{location-id\}/models/general/nmt`.
         */
        model?: string | null;
    }
    /**
     * Configures which glossary should be used for a specific target language, and defines options for applying that glossary.
     */
    export interface Schema$TranslateTextGlossaryConfig {
        /**
         * Required. Specifies the glossary used for this translation. Use this format: projects/x/locations/x/glossaries/x
         */
        glossary?: string | null;
        /**
         * Optional. Indicates match is case-insensitive. Default value is false if missing.
         */
        ignoreCase?: boolean | null;
    }
    /**
     * The request message for synchronous translation.
     */
    export interface Schema$TranslateTextRequest {
        /**
         * Required. The content of the input in string format. We recommend the total content be less than 30k codepoints. The max length of this field is 1024. Use BatchTranslateText for larger text.
         */
        contents?: string[] | null;
        /**
         * Optional. Glossary to be applied. The glossary must be within the same region (have the same location-id) as the model, otherwise an INVALID_ARGUMENT (400) error is returned.
         */
        glossaryConfig?: Schema$TranslateTextGlossaryConfig;
        /**
         * Optional. The labels with user-defined metadata for the request. Label keys and values can be no longer than 63 characters (Unicode codepoints), can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter. See https://cloud.google.com/translate/docs/labels for more information.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. The format of the source text, for example, "text/html", "text/plain". If left blank, the MIME type defaults to "text/html".
         */
        mimeType?: string | null;
        /**
         * Optional. The `model` type requested for this translation. The format depends on model type: - AutoML Translation models: `projects/{project-number-or-id\}/locations/{location-id\}/models/{model-id\}` - General (built-in) models: `projects/{project-number-or-id\}/locations/{location-id\}/models/general/nmt`, For global (non-regionalized) requests, use `location-id` `global`. For example, `projects/{project-number-or-id\}/locations/global/models/general/nmt`. If not provided, the default Google model (NMT) will be used
         */
        model?: string | null;
        /**
         * Optional. The BCP-47 language code of the input text if known, for example, "en-US" or "sr-Latn". Supported language codes are listed in Language Support. If the source language isn't specified, the API attempts to identify the source language automatically and returns the source language within the response.
         */
        sourceLanguageCode?: string | null;
        /**
         * Required. The BCP-47 language code to use for translation of the input text, set to one of the language codes listed in Language Support.
         */
        targetLanguageCode?: string | null;
    }
    export interface Schema$TranslateTextResponse {
        /**
         * Text translation responses if a glossary is provided in the request. This can be the same as `translations` if no terms apply. This field has the same length as `contents`.
         */
        glossaryTranslations?: Schema$Translation[];
        /**
         * Text translation responses with no glossary applied. This field has the same length as `contents`.
         */
        translations?: Schema$Translation[];
    }
    /**
     * A single translation response.
     */
    export interface Schema$Translation {
        /**
         * The BCP-47 language code of source text in the initial request, detected automatically, if no source language was passed within the initial request. If the source language was passed, auto-detection of the language does not occur and this field is empty.
         */
        detectedLanguageCode?: string | null;
        /**
         * The `glossary_config` used for this translation.
         */
        glossaryConfig?: Schema$TranslateTextGlossaryConfig;
        /**
         * Only present when `model` is present in the request. `model` here is normalized to have project number. For example: If the `model` requested in TranslationTextRequest is `projects/{project-id\}/locations/{location-id\}/models/general/nmt` then `model` here would be normalized to `projects/{project-number\}/locations/{location-id\}/models/general/nmt`.
         */
        model?: string | null;
        /**
         * Text translated into the target language. If an error occurs during translation, this field might be excluded from the response.
         */
        translatedText?: string | null;
    }
    /**
     * The request message for Operations.WaitOperation.
     */
    export interface Schema$WaitOperationRequest {
        /**
         * The maximum duration to wait before timing out. If left blank, the wait will be at most the time permitted by the underlying HTTP/RPC protocol. If RPC context deadline is also specified, the shorter one will be used.
         */
        timeout?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
        /**
         * Detects the language of text within a request.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        detectLanguage(params: Params$Resource$Projects$Detectlanguage, options: StreamMethodOptions): GaxiosPromise<Readable>;
        detectLanguage(params?: Params$Resource$Projects$Detectlanguage, options?: MethodOptions): GaxiosPromise<Schema$DetectLanguageResponse>;
        detectLanguage(params: Params$Resource$Projects$Detectlanguage, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        detectLanguage(params: Params$Resource$Projects$Detectlanguage, options: MethodOptions | BodyResponseCallback<Schema$DetectLanguageResponse>, callback: BodyResponseCallback<Schema$DetectLanguageResponse>): void;
        detectLanguage(params: Params$Resource$Projects$Detectlanguage, callback: BodyResponseCallback<Schema$DetectLanguageResponse>): void;
        detectLanguage(callback: BodyResponseCallback<Schema$DetectLanguageResponse>): void;
        /**
         * Returns a list of supported languages for translation.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getSupportedLanguages(params: Params$Resource$Projects$Getsupportedlanguages, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getSupportedLanguages(params?: Params$Resource$Projects$Getsupportedlanguages, options?: MethodOptions): GaxiosPromise<Schema$SupportedLanguages>;
        getSupportedLanguages(params: Params$Resource$Projects$Getsupportedlanguages, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getSupportedLanguages(params: Params$Resource$Projects$Getsupportedlanguages, options: MethodOptions | BodyResponseCallback<Schema$SupportedLanguages>, callback: BodyResponseCallback<Schema$SupportedLanguages>): void;
        getSupportedLanguages(params: Params$Resource$Projects$Getsupportedlanguages, callback: BodyResponseCallback<Schema$SupportedLanguages>): void;
        getSupportedLanguages(callback: BodyResponseCallback<Schema$SupportedLanguages>): void;
        /**
         * Translates input text and returns translated text.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        translateText(params: Params$Resource$Projects$Translatetext, options: StreamMethodOptions): GaxiosPromise<Readable>;
        translateText(params?: Params$Resource$Projects$Translatetext, options?: MethodOptions): GaxiosPromise<Schema$TranslateTextResponse>;
        translateText(params: Params$Resource$Projects$Translatetext, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        translateText(params: Params$Resource$Projects$Translatetext, options: MethodOptions | BodyResponseCallback<Schema$TranslateTextResponse>, callback: BodyResponseCallback<Schema$TranslateTextResponse>): void;
        translateText(params: Params$Resource$Projects$Translatetext, callback: BodyResponseCallback<Schema$TranslateTextResponse>): void;
        translateText(callback: BodyResponseCallback<Schema$TranslateTextResponse>): void;
    }
    export interface Params$Resource$Projects$Detectlanguage extends StandardParameters {
        /**
         * Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id\}/locations/{location-id\}` or `projects/{project-number-or-id\}`. For global calls, use `projects/{project-number-or-id\}/locations/global` or `projects/{project-number-or-id\}`. Only models within the same region (has same location-id) can be used. Otherwise an INVALID_ARGUMENT (400) error is returned.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DetectLanguageRequest;
    }
    export interface Params$Resource$Projects$Getsupportedlanguages extends StandardParameters {
        /**
         * Optional. The language to use to return localized, human readable names of supported languages. If missing, then display names are not returned in a response.
         */
        displayLanguageCode?: string;
        /**
         * Optional. Get supported languages of this model. The format depends on model type: - AutoML Translation models: `projects/{project-number-or-id\}/locations/{location-id\}/models/{model-id\}` - General (built-in) models: `projects/{project-number-or-id\}/locations/{location-id\}/models/general/nmt`, Returns languages supported by the specified model. If missing, we get supported languages of Google general NMT model.
         */
        model?: string;
        /**
         * Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id\}` or `projects/{project-number-or-id\}/locations/{location-id\}`. For global calls, use `projects/{project-number-or-id\}/locations/global` or `projects/{project-number-or-id\}`. Non-global location is required for AutoML models. Only models within the same region (have same location-id) can be used, otherwise an INVALID_ARGUMENT (400) error is returned.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Translatetext extends StandardParameters {
        /**
         * Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id\}` or `projects/{project-number-or-id\}/locations/{location-id\}`. For global calls, use `projects/{project-number-or-id\}/locations/global` or `projects/{project-number-or-id\}`. Non-global location is required for requests using AutoML models or custom glossaries. Models and glossaries must be within the same region (have same location-id), otherwise an INVALID_ARGUMENT (400) error is returned.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TranslateTextRequest;
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        glossaries: Resource$Projects$Locations$Glossaries;
        operations: Resource$Projects$Locations$Operations;
        constructor(context: APIRequestContext);
        /**
         * Translates a large volume of document in asynchronous batch mode. This function provides real-time output as the inputs are being processed. If caller cancels a request, the partial results (for an input file, it's all or nothing) may still be available on the specified output location. This call returns immediately and you can use google.longrunning.Operation.name to poll the status of the call.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        batchTranslateDocument(params: Params$Resource$Projects$Locations$Batchtranslatedocument, options: StreamMethodOptions): GaxiosPromise<Readable>;
        batchTranslateDocument(params?: Params$Resource$Projects$Locations$Batchtranslatedocument, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        batchTranslateDocument(params: Params$Resource$Projects$Locations$Batchtranslatedocument, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        batchTranslateDocument(params: Params$Resource$Projects$Locations$Batchtranslatedocument, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        batchTranslateDocument(params: Params$Resource$Projects$Locations$Batchtranslatedocument, callback: BodyResponseCallback<Schema$Operation>): void;
        batchTranslateDocument(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Translates a large volume of text in asynchronous batch mode. This function provides real-time output as the inputs are being processed. If caller cancels a request, the partial results (for an input file, it's all or nothing) may still be available on the specified output location. This call returns immediately and you can use google.longrunning.Operation.name to poll the status of the call.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        batchTranslateText(params: Params$Resource$Projects$Locations$Batchtranslatetext, options: StreamMethodOptions): GaxiosPromise<Readable>;
        batchTranslateText(params?: Params$Resource$Projects$Locations$Batchtranslatetext, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        batchTranslateText(params: Params$Resource$Projects$Locations$Batchtranslatetext, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        batchTranslateText(params: Params$Resource$Projects$Locations$Batchtranslatetext, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        batchTranslateText(params: Params$Resource$Projects$Locations$Batchtranslatetext, callback: BodyResponseCallback<Schema$Operation>): void;
        batchTranslateText(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Detects the language of text within a request.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        detectLanguage(params: Params$Resource$Projects$Locations$Detectlanguage, options: StreamMethodOptions): GaxiosPromise<Readable>;
        detectLanguage(params?: Params$Resource$Projects$Locations$Detectlanguage, options?: MethodOptions): GaxiosPromise<Schema$DetectLanguageResponse>;
        detectLanguage(params: Params$Resource$Projects$Locations$Detectlanguage, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        detectLanguage(params: Params$Resource$Projects$Locations$Detectlanguage, options: MethodOptions | BodyResponseCallback<Schema$DetectLanguageResponse>, callback: BodyResponseCallback<Schema$DetectLanguageResponse>): void;
        detectLanguage(params: Params$Resource$Projects$Locations$Detectlanguage, callback: BodyResponseCallback<Schema$DetectLanguageResponse>): void;
        detectLanguage(callback: BodyResponseCallback<Schema$DetectLanguageResponse>): void;
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$Location>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Returns a list of supported languages for translation.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getSupportedLanguages(params: Params$Resource$Projects$Locations$Getsupportedlanguages, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getSupportedLanguages(params?: Params$Resource$Projects$Locations$Getsupportedlanguages, options?: MethodOptions): GaxiosPromise<Schema$SupportedLanguages>;
        getSupportedLanguages(params: Params$Resource$Projects$Locations$Getsupportedlanguages, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getSupportedLanguages(params: Params$Resource$Projects$Locations$Getsupportedlanguages, options: MethodOptions | BodyResponseCallback<Schema$SupportedLanguages>, callback: BodyResponseCallback<Schema$SupportedLanguages>): void;
        getSupportedLanguages(params: Params$Resource$Projects$Locations$Getsupportedlanguages, callback: BodyResponseCallback<Schema$SupportedLanguages>): void;
        getSupportedLanguages(callback: BodyResponseCallback<Schema$SupportedLanguages>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$ListLocationsResponse>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        /**
         * Translates documents in synchronous mode.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        translateDocument(params: Params$Resource$Projects$Locations$Translatedocument, options: StreamMethodOptions): GaxiosPromise<Readable>;
        translateDocument(params?: Params$Resource$Projects$Locations$Translatedocument, options?: MethodOptions): GaxiosPromise<Schema$TranslateDocumentResponse>;
        translateDocument(params: Params$Resource$Projects$Locations$Translatedocument, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        translateDocument(params: Params$Resource$Projects$Locations$Translatedocument, options: MethodOptions | BodyResponseCallback<Schema$TranslateDocumentResponse>, callback: BodyResponseCallback<Schema$TranslateDocumentResponse>): void;
        translateDocument(params: Params$Resource$Projects$Locations$Translatedocument, callback: BodyResponseCallback<Schema$TranslateDocumentResponse>): void;
        translateDocument(callback: BodyResponseCallback<Schema$TranslateDocumentResponse>): void;
        /**
         * Translates input text and returns translated text.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        translateText(params: Params$Resource$Projects$Locations$Translatetext, options: StreamMethodOptions): GaxiosPromise<Readable>;
        translateText(params?: Params$Resource$Projects$Locations$Translatetext, options?: MethodOptions): GaxiosPromise<Schema$TranslateTextResponse>;
        translateText(params: Params$Resource$Projects$Locations$Translatetext, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        translateText(params: Params$Resource$Projects$Locations$Translatetext, options: MethodOptions | BodyResponseCallback<Schema$TranslateTextResponse>, callback: BodyResponseCallback<Schema$TranslateTextResponse>): void;
        translateText(params: Params$Resource$Projects$Locations$Translatetext, callback: BodyResponseCallback<Schema$TranslateTextResponse>): void;
        translateText(callback: BodyResponseCallback<Schema$TranslateTextResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Batchtranslatedocument extends StandardParameters {
        /**
         * Required. Location to make a regional call. Format: `projects/{project-number-or-id\}/locations/{location-id\}`. The `global` location is not supported for batch translation. Only AutoML Translation models or glossaries within the same region (have the same location-id) can be used, otherwise an INVALID_ARGUMENT (400) error is returned.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$BatchTranslateDocumentRequest;
    }
    export interface Params$Resource$Projects$Locations$Batchtranslatetext extends StandardParameters {
        /**
         * Required. Location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id\}/locations/{location-id\}`. The `global` location is not supported for batch translation. Only AutoML Translation models or glossaries within the same region (have the same location-id) can be used, otherwise an INVALID_ARGUMENT (400) error is returned.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$BatchTranslateTextRequest;
    }
    export interface Params$Resource$Projects$Locations$Detectlanguage extends StandardParameters {
        /**
         * Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id\}/locations/{location-id\}` or `projects/{project-number-or-id\}`. For global calls, use `projects/{project-number-or-id\}/locations/global` or `projects/{project-number-or-id\}`. Only models within the same region (has same location-id) can be used. Otherwise an INVALID_ARGUMENT (400) error is returned.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DetectLanguageRequest;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Getsupportedlanguages extends StandardParameters {
        /**
         * Optional. The language to use to return localized, human readable names of supported languages. If missing, then display names are not returned in a response.
         */
        displayLanguageCode?: string;
        /**
         * Optional. Get supported languages of this model. The format depends on model type: - AutoML Translation models: `projects/{project-number-or-id\}/locations/{location-id\}/models/{model-id\}` - General (built-in) models: `projects/{project-number-or-id\}/locations/{location-id\}/models/general/nmt`, Returns languages supported by the specified model. If missing, we get supported languages of Google general NMT model.
         */
        model?: string;
        /**
         * Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id\}` or `projects/{project-number-or-id\}/locations/{location-id\}`. For global calls, use `projects/{project-number-or-id\}/locations/global` or `projects/{project-number-or-id\}`. Non-global location is required for AutoML models. Only models within the same region (have same location-id) can be used, otherwise an INVALID_ARGUMENT (400) error is returned.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Projects$Locations$Translatedocument extends StandardParameters {
        /**
         * Required. Location to make a regional call. Format: `projects/{project-number-or-id\}/locations/{location-id\}`. For global calls, use `projects/{project-number-or-id\}/locations/global`. Non-global location is required for requests using AutoML models or custom glossaries. Models and glossaries must be within the same region (have the same location-id), otherwise an INVALID_ARGUMENT (400) error is returned.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TranslateDocumentRequest;
    }
    export interface Params$Resource$Projects$Locations$Translatetext extends StandardParameters {
        /**
         * Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id\}` or `projects/{project-number-or-id\}/locations/{location-id\}`. For global calls, use `projects/{project-number-or-id\}/locations/global` or `projects/{project-number-or-id\}`. Non-global location is required for requests using AutoML models or custom glossaries. Models and glossaries must be within the same region (have same location-id), otherwise an INVALID_ARGUMENT (400) error is returned.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TranslateTextRequest;
    }
    export class Resource$Projects$Locations$Glossaries {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a glossary and returns the long-running operation. Returns NOT_FOUND, if the project doesn't exist.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Glossaries$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Glossaries$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Glossaries$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Glossaries$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Glossaries$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a glossary, or cancels glossary construction if the glossary isn't created yet. Returns NOT_FOUND, if the glossary doesn't exist.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Glossaries$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Glossaries$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Glossaries$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Glossaries$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Glossaries$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets a glossary. Returns NOT_FOUND, if the glossary doesn't exist.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Glossaries$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Glossaries$Get, options?: MethodOptions): GaxiosPromise<Schema$Glossary>;
        get(params: Params$Resource$Projects$Locations$Glossaries$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Glossaries$Get, options: MethodOptions | BodyResponseCallback<Schema$Glossary>, callback: BodyResponseCallback<Schema$Glossary>): void;
        get(params: Params$Resource$Projects$Locations$Glossaries$Get, callback: BodyResponseCallback<Schema$Glossary>): void;
        get(callback: BodyResponseCallback<Schema$Glossary>): void;
        /**
         * Lists glossaries in a project. Returns NOT_FOUND, if the project doesn't exist.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Glossaries$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Glossaries$List, options?: MethodOptions): GaxiosPromise<Schema$ListGlossariesResponse>;
        list(params: Params$Resource$Projects$Locations$Glossaries$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Glossaries$List, options: MethodOptions | BodyResponseCallback<Schema$ListGlossariesResponse>, callback: BodyResponseCallback<Schema$ListGlossariesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Glossaries$List, callback: BodyResponseCallback<Schema$ListGlossariesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListGlossariesResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Glossaries$Create extends StandardParameters {
        /**
         * Required. The project name.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Glossary;
    }
    export interface Params$Resource$Projects$Locations$Glossaries$Delete extends StandardParameters {
        /**
         * Required. The name of the glossary to delete.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Glossaries$Get extends StandardParameters {
        /**
         * Required. The name of the glossary to retrieve.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Glossaries$List extends StandardParameters {
        /**
         * Optional. Filter specifying constraints of a list operation. Specify the constraint by the format of "key=value", where key must be "src" or "tgt", and the value must be a valid language code. For multiple restrictions, concatenate them by "AND" (uppercase only), such as: "src=en-US AND tgt=zh-CN". Notice that the exact match is used here, which means using 'en-US' and 'en' can lead to different results, which depends on the language code you used when you create the glossary. For the unidirectional glossaries, the "src" and "tgt" add restrictions on the source and target language code separately. For the equivalent term set glossaries, the "src" and/or "tgt" add restrictions on the term set. For example: "src=en-US AND tgt=zh-CN" will only pick the unidirectional glossaries which exactly match the source language code as "en-US" and the target language code "zh-CN", but all equivalent term set glossaries which contain "en-US" and "zh-CN" in their language set will be picked. If missing, no filtering is performed.
         */
        filter?: string;
        /**
         * Optional. Requested page size. The server may return fewer glossaries than requested. If unspecified, the server picks an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. A token identifying a page of results the server should return. Typically, this is the value of [ListGlossariesResponse.next_page_token] returned from the previous call to `ListGlossaries` method. The first page is returned if `page_token`is empty or missing.
         */
        pageToken?: string;
        /**
         * Required. The name of the project from which to list all of the glossaries.
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ListOperationsResponse>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        /**
         * Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        wait(params: Params$Resource$Projects$Locations$Operations$Wait, options: StreamMethodOptions): GaxiosPromise<Readable>;
        wait(params?: Params$Resource$Projects$Locations$Operations$Wait, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        wait(params: Params$Resource$Projects$Locations$Operations$Wait, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        wait(params: Params$Resource$Projects$Locations$Operations$Wait, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        wait(params: Params$Resource$Projects$Locations$Operations$Wait, callback: BodyResponseCallback<Schema$Operation>): void;
        wait(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Wait extends StandardParameters {
        /**
         * The name of the operation resource to wait on.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$WaitOperationRequest;
    }
    export {};
}
