import axios from "axios";

const axiosClient = axios.create({
  baseURL: "http://localhost:5001", // hoặc http://localhost:8000
  headers: {
    "Content-Type": "application/json",
  },
});

// Gán token vào header Authorization
export const setRestAuth = (token) => {
  if (token) {
    axiosClient.defaults.headers.common["Authorization"] = `Basic ${token}`;
  }
};

// Xoá token Authorization
export const deleteAuthorization = () => {
  delete axiosClient.defaults.headers.common.Authorization;
  delete axiosClient.defaults.headers.common["Authorization"];
};

// interceptors cho axiosClient (đúng đối tượng đang dùng)
axiosClient.interceptors.request.use(
  function (config) {
    // Gửi request đi
    return config;
  },
  function (error) {
    // Lỗi khi gửi request
    return Promise.reject(error);
  }
);

axiosClient.interceptors.response.use(
  function (response) {
    // Xử lý response trả về
    return response;
  },
  function (error) {
    // <PERSON><PERSON> lý lỗi từ response
    return Promise.reject(error);
  }
);

export default axiosClient;
