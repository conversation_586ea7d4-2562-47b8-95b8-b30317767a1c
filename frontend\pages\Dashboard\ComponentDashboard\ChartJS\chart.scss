.title {
  font-family: "<PERSON><PERSON>", sans-serif;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.chart-container {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
// ../../ChartJS/chart.scss

.chart-container {
  padding: 20px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-align: center; // Căn giữa nội dung

  .chart-title {
    font-size: 20px; // Kích thước tiêu đề
    color: #333; // M<PERSON>u sắc tiêu đề
    margin-bottom: 10px;
  }

  .pie-chart {
    max-width: 300px; // Giới hạn kích thước biểu đồ
    margin: 0 auto; // Căn gi<PERSON>a biểu đồ
  }

  .chart-details {
    margin-top: 15px; // Khoảng cách trên
    font-size: 14px; // <PERSON><PERSON>ch thước chữ cho chi tiết

    .chart-item {
      margin: 5px 0; // Khoảng cách giữa các mục
    }

    .chart-label {
      font-weight: bold; // Làm đậm nhãn
    }

    .chart-quantity {
      color: #555; // Màu sắc cho số lượng
    }

    .chart-total {
      margin-top: 10px; // Khoảng cách trên
      font-weight: bold; // Làm đậm tổng số lượng
      color: #000; // Màu sắc tổng số lượng
    }
  }
}
