import axiosClient from "./AxiosClient";
const getAllProducts = async (page = 1, limit = 9) => {
  const response = await axiosClient.get(`/product/findAll?page=${page}&limit=${limit}`);
  return response.data;
};
const getProduct = async (id) => {
  try {
    console.log("Product ID:", id); // Kiểm tra giá trị id
    const response = await axiosClient.get(`/product/findOne`, {
      params: { _id: id },
    });
    return response.data;
  } catch (error) {
    console.error("API call failed:", error);
    throw error;
  }
};
const getAllCategories = async () => {
  // Backend không có endpoint categories riêng, lấy categories từ products
  const response = await axiosClient.get("/product/findAll");
  const products = response.data.products || [];
  // Lấy danh sách categories unique từ products
  const categories = [...new Set(products.map(p => p.category).filter(Boolean))];
  return { categories: categories.map(cat => ({ name: cat, _id: cat })) };
};
const getProductByCategory = async (categoryName) => {
  // Tìm products theo category name
  const filter = JSON.stringify({ category: categoryName });
  const response = await axiosClient.get(`/product/findAll?filter=${encodeURIComponent(filter)}`);
  return response.data;
};

const getProductImages = async (productId) => {
  try {
    const response = await axiosClient.get(`/product/image/find?filter=${encodeURIComponent(JSON.stringify({ productId }))}`);
    return response.data;
  } catch (error) {
    console.error("Get product images failed:", error);
    throw error;
  }
};

export { getAllProducts, getProduct, getAllCategories, getProductByCategory, getProductImages };
