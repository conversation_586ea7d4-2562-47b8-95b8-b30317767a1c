/* Card container */
.card-container {
  border: 1px solid #e0e0e0; /* Đ<PERSON>ờng viền màu xám nhạt cho card */
  border-radius: 10px; /* <PERSON> góc cho card */
  overflow: hidden; /* Ẩn phần ngoài giới hạn của card */
  transition: box-shadow 0.3s ease, transform 0.3s ease; /* Hiệu ứng chuyển động cho shadow và biến đổi hình */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Tạo đổ bóng nhẹ cho card */

  /* Hiệu ứng hover cho card */
  &:hover {
    transform: translateY(-5px); /* Di chuyển card lên một chút khi hover */
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2); /* <PERSON><PERSON> bóng đậm hơn khi hover */
  }

  /* Nhãn trạng thái (In stock/Out of stock) */
  .status-label {
    display: inline-block;
    padding: 0.25rem 0.5rem; /* <PERSON><PERSON><PERSON>ng c<PERSON>ch cho nhãn */
    font-size: 0.875rem; /* <PERSON><PERSON><PERSON> thư<PERSON> chữ */
    border-radius: 5px; /* Bo góc nhãn */
    color: #fff; /* Màu chữ trắng */
    text-align: center;
    font-weight: 600; /* In đậm chữ */

    /* Trạng thái còn hàng */
    &.in-stock {
      background-color: #28a745; /* Màu xanh cho "In stock" */
    }

    /* Trạng thái hết hàng */
    &.out-of-stock {
      background-color: #dc3545; /* Màu đỏ cho "Out of stock" */
    }
  }

  /* Mô tả sản phẩm */
  .description {
    font-size: 0.9rem;
    color: #444444; /* Màu chữ xám đậm cho mô tả */
    margin-top: 10px; /* Khoảng cách từ trên */
    line-height: 1.5; /* Cách dòng 1.5 để dễ đọc */
    text-align: justify; /* Căn đều phần mô tả */
  }

  /* Hình ảnh sản phẩm */
  .product-image {
    width: 100%; /* Chiều rộng ảnh chiếm toàn bộ chiều rộng của container */
    height: 200px; /* Chiều cao cố định cho ảnh */
    object-fit: cover; /* Đảm bảo ảnh không bị méo, cắt bớt nếu cần */
    border-radius: 10px; /* Bo góc cho ảnh */
    transition: transform 0.3s ease; /* Hiệu ứng chuyển động khi hover */
    display: block; /* Làm ảnh hiển thị như block */
    margin: 0 auto; /* Căn giữa ảnh */
    padding-top: 10px; /* Khoảng cách trên cho ảnh */
    padding-bottom: 10px; /* Khoảng cách dưới cho ảnh */
  }

  /* Nút chính (ví dụ: Xem chi tiết sản phẩm) */
  .btn-primary {
    background-color: #007bff; /* Màu nền xanh dương */
    border-color: #007bff;
    padding: 0.5rem 1rem; /* Padding trái phải */
    font-size: 0.875rem; /* Kích thước chữ */
    font-weight: bold; /* Chữ in đậm */
    border-radius: 5px; /* Bo góc cho nút */
    transition: background-color 0.3s ease, border-color 0.3s ease; /* Hiệu ứng chuyển màu khi hover */
    display: inline-block; /* Đảm bảo nút không bị kéo dài */
    margin: 0 auto; /* Căn giữa nút */
  }

  /* Hiệu ứng hover cho nút */
  .btn-primary:hover {
    background-color: #0056b3; /* Màu nền thay đổi khi hover */
    border-color: #004085;
  }

  /* Nút xóa (danger) */
  .btn-danger {
    background-color: #dc3545; /* Màu đỏ cho nút xóa */
    border-color: #dc3545;
    padding: 0.5rem 1rem; /* Padding trái phải */
    font-size: 0.875rem;
    font-weight: bold;
    border-radius: 5px;
    transition: background-color 0.3s ease, border-color 0.3s ease; /* Hiệu ứng hover cho nút */

    /* Hiệu ứng hover cho nút xóa */
    &:hover {
      background-color: #c82333; /* Màu nền thay đổi khi hover */
      border-color: #bd2130;
    }
  }

  /* Các nút hành động (sửa, xóa, v.v.) */
  .action-buttons {
    display: flex;
    gap: 1rem; /* Khoảng cách giữa các nút */
    justify-content: center; /* Căn giữa các nút */
    flex-wrap: nowrap; /* Không xuống dòng */
    margin-top: 10px; /* Khoảng cách từ trên */
  }

  /* Tên sản phẩm */
  .product-name {
    font-weight: bold; /* Đảm bảo tên sản phẩm in đậm */
  }
}

/* Media query cho tablet (740px - 1024px) */
@media (min-width: 740px) and (max-width: 1024px) {
  .card-container .action-buttons {
    display: block; /* Hiển thị các nút hành động theo dạng block */
    margin-top: 10px;
  }

  .card-container .pt-2 {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between; /* Căn đều các phần tử trong pt-2 */
  }

  .card-container .status-label {
    margin-bottom: 10px; /* Thêm khoảng cách dưới nhãn trạng thái */
  }

  .card-container .mx-2 {
    display: none; /* Ẩn các phần tử có class mx-2 */
  }

  .card-container .action-buttons {
    gap: 1rem; /* Khoảng cách giữa các nút */
  }
}

/* Media query cho màn hình nhỏ hơn 540px */
@media (max-width: 540px) {
  .card-container .action-buttons {
    display: block; /* Hiển thị các nút hành động theo dạng block */
    margin-top: 10px;
    gap: 0.5rem; /* Thêm khoảng cách giữa các nút khi ở màn hình nhỏ */
  }

  .card-container .pt-2 {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between; /* Căn đều các phần tử trong pt-2 */
  }

  .card-container .status-label {
    margin-bottom: 10px; /* Thêm khoảng cách dưới nhãn trạng thái */
  }

  .card-container .mx-2 {
    display: none; /* Ẩn các phần tử có class mx-2 */
  }
}

/* Modal chỉnh sửa sản phẩm */
.modal-content {
  border-radius: 10px; /* Bo góc modal */
  padding: 20px; /* Khoảng cách bên trong modal */
  background-color: #fff; /* Nền trắng */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Đổ bóng cho modal */
}

.modal-header {
  border-bottom: none; /* Bỏ đường viền dưới modal header */
  font-weight: bold; /* In đậm cho tiêu đề modal */
  font-size: 1.25rem; /* Kích thước chữ tiêu đề modal */
  color: #333; /* Màu chữ */
}

.modal-body {
  padding: 20px; /* Khoảng cách bên trong body modal */

  label {
    display: block;
    font-size: 1rem; /* Kích thước chữ cho label */
    font-weight: 500; /* Chữ vừa phải */
    margin-bottom: 8px; /* Khoảng cách dưới label */
    color: #555; /* Màu chữ */
  }

  /* Input và textarea trong modal */
  input,
  textarea {
    width: 100%; /* Đảm bảo các trường chiếm toàn bộ chiều rộng */
    padding: 10px; /* Khoảng cách bên trong input */
    margin-bottom: 15px; /* Khoảng cách dưới các input */
    border: 1px solid #ccc; /* Viền cho input */
    border-radius: 5px; /* Bo góc input */
    font-size: 1rem; /* Kích thước chữ */
    outline: none; /* Bỏ viền mặc định khi chọn input */

    &:focus {
      border-color: #007bff; /* Viền xanh khi input được focus */
      box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); /* Đổ bóng nhẹ khi focus */
    }
  }

  /* Textarea có thể thay đổi kích thước */
  textarea {
    resize: vertical; /* Cho phép thay đổi chiều cao textarea */
  }
}
