import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import App from "./App.jsx";
import { ContextProvider } from "./context/context.jsx";
import "./index.css";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <BrowserRouter>
      <ContextProvider >
        <App/>
      </ContextProvider>
    </BrowserRouter>
  </StrictMode>
);
