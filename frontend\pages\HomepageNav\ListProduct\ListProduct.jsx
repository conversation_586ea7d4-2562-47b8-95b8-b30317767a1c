import React, { useEffect, useState } from "react";
import { Container, Row, Col, Spinner } from "reactstrap"; // Giữ nguyên các import từ reactstrap
import { getAllProducts } from "../../Api/product";
import CardProduct from "../ListProduct/CardProduct/CardProduct";
import "../ListProduct/styleListProducts.scss";

// Component ProductView để hiển thị danh sách sản phẩm
const ProductView = () => {
  // Khởi tạo state products để lưu trữ danh sách sản phẩm, ban đầu là mảng rỗng
  const [products, setProducts] = useState([]);
  // Khởi tạo state loading để theo dõi trạng thái tải dữ liệu, ban đầu là true (đang tải)
  const [loading, setLoading] = useState(true);

  // useEffect để thực hiện tải dữ liệu khi component được render lần đầu
  useEffect(() => {
    // Hàm fetchProducts để lấy danh sách sản phẩm từ API
    const fetchProducts = async () => {
      try {
        // Gọi hàm API getAllProducts để lấy dữ liệu
        const response = await getAllProducts();
        // Kiểm tra cấu trúc dữ liệu trả về, nếu hợp lệ thì cập nhật state products
        if (response.products && Array.isArray(response.products)) {
          setProducts(response.products);
        } else {
          console.error("Dữ liệu trả về không đúng cấu trúc:", response);
          setProducts([]); // Đảm bảo products là mảng nếu API trả về sai
        }
      } catch (error) {
        // Nếu xảy ra lỗi trong quá trình tải, log lỗi ra console
        console.error("Lỗi khi tải sản phẩm:", error);
        setProducts([]); // Đảm bảo products là mảng nếu có lỗi
      } finally {
        // Kết thúc quá trình tải dữ liệu, cập nhật state loading thành false
        setLoading(false);
      }
    };
    // Gọi hàm fetchProducts
    fetchProducts();
  }, []); // Mảng phụ thuộc rỗng, useEffect chỉ chạy một lần khi component mount

  // Hàm xử lý khi xóa sản phẩm thành công
  const handleDeleteSuccess = (deletedProductId) => {
    // Cập nhật state products, lọc bỏ sản phẩm có _id trùng với deletedProductId
    setProducts(products.filter((product) => product._id !== deletedProductId));
  };

  // Hàm xử lý khi cập nhật sản phẩm thành công
  const handleUpdateSuccess = (updatedProduct) => {
    // Cập nhật state products, thay thế sản phẩm có _id trùng với updatedProduct._id bằng sản phẩm mới
    // Đảm bảo updatedProduct có _id để so sánh
    if (!updatedProduct || !updatedProduct._id) {
      console.error("Sản phẩm cập nhật không hợp lệ:", updatedProduct);
      return;
    }
    setProducts(
      products.map((product) =>
        product._id === updatedProduct._id ? updatedProduct : product
      )
    );
  };

  return (
    // Container bao bọc nội dung hiển thị của component
    <Container>
      {/* Tiêu đề danh sách sản phẩm */}
      <h2 className="title pt-3 pb-0">DANH SÁCH SẢN PHẨM</h2>

      {/* Phần Search Bar/Filter mới được thêm vào */}
      <div className="bg-light py-3 my-3"> {/* Thêm my-3 để có khoảng cách trên dưới */}
        <Row className="justify-content-center">
          <Col md={10}>
            <div className="search-bar"> {/* Bạn cần định nghĩa style cho class này trong styleListProducts.scss */}
              <select className="form-select me-2 mb-2 mb-md-0"> {/* Thêm margin */}
                <option defaultValue>Hệ thống sản phẩm</option>
                <option value="1">One</option>
                <option value="2">Two</option>
                <option value="3">Three</option>
              </select>
              <select className="form-select me-2 mb-2 mb-md-0"> {/* Thêm margin */}
                <option defaultValue>Dashboard</option>
                <option value="1">One</option>
                <option value="2">Two</option>
                <option value="3">Three</option>
              </select>
              <input
                type="text"
                className="form-control me-2 mb-2 mb-md-0" /* Thêm margin */
                placeholder="Nhân viên"
              />
              <input
                type="text"
                className="form-control"
                placeholder="Khách hàng"
              />
            </div>
          </Col>
        </Row>
      </div>

      <div className="pt-3 pb-3">
        {loading ? (
          <div className="text-center">
            <Spinner color="primary" />
          </div>
        ) : (
          <Row>

            {Array.isArray(products) && products.length > 0 ? (
              products.map((product) => (

                < Col key={product._id} md={6} className="mb-4" >

                  < CardProduct
                    product={product}
                    onDeleteSuccess={handleDeleteSuccess}
                    onUpdateSuccess={handleUpdateSuccess}
                  />
                </Col>
              ))
            ) : (

              <Col>
                <div className="text-center">Không có sản phẩm nào.</div>
              </Col>
            )}
          </Row>
        )}
      </div>
    </Container >
  );
};

export default ProductView;