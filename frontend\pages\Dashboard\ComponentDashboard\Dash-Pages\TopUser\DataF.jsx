// DataF.js

const fakeUserData = [
  {
    _id: "672d0521d53c3cc9ba4d568d",
    fullName: "Nguyễn <PERSON>",
    revenue: 15000000, // <PERSON><PERSON>h số giả
    totalPurchases: 5, // Số lần mua giả
    products: [
      { name: "Sản phẩm 1", quantity: 2 },
      { name: "Sản phẩm 2", quantity: 3 },
    ],
  },
  {
    _id: "672d0521d53c3cc9ba4d568e",
    fullName: "Trần Hải Dương",
    revenue: 22000000, // <PERSON>anh số giả
    totalPurchases: 8, // Số lần mua giả
    products: [
      { name: "Sản phẩm A", quantity: 1 },
      { name: "Sản phẩm B", quantity: 4 },
    ],
  },
  {
    _id: "672d0521d53c3cc9ba4d568f",
    fullName: "<PERSON> Xuân Nhân",
    revenue: 18000000, // <PERSON><PERSON>h số giả
    totalPurchases: 6, // Số lần mua giả
    products: [
      { name: "Sản phẩm X", quantity: 3 },
      { name: "Sản phẩm Y", quantity: 2 },
    ],
  },
  {
    _id: "672d0521d53c3cc9ba4d5690",
    fullName: "Lê Minh Hải",
    revenue: 12000000, // Doanh số giả
    totalPurchases: 4, // Số lần mua giả
    products: [
      { name: "Sản phẩm M", quantity: 1 },
      { name: "Sản phẩm N", quantity: 3 },
    ],
  },
  {
    _id: "672d0521d53c3cc9ba4d5691",
    fullName: "Phạm Văn Tuấn",
    revenue: 25000000, // Doanh số giả
    totalPurchases: 10, // Số lần mua giả
    products: [
      { name: "Sản phẩm C", quantity: 2 },
      { name: "Sản phẩm D", quantity: 5 },
    ],
  },
  {
    _id: "672d0521d53c3cc9ba4d5692",
    fullName: "Đặng Thu Hà",
    revenue: 20000000, // Doanh số giả
    totalPurchases: 7, // Số lần mua giả
    products: [
      { name: "Sản phẩm P", quantity: 3 },
      { name: "Sản phẩm Q", quantity: 4 },
    ],
  },
  {
    _id: "672d0521d53c3cc9ba4d5693",
    fullName: "Vũ Minh Đức",
    revenue: 17000000, // Doanh số giả
    totalPurchases: 6, // Số lần mua giả
    products: [
      { name: "Sản phẩm E", quantity: 2 },
      { name: "Sản phẩm F", quantity: 4 },
    ],
  },
];

export default fakeUserData;
